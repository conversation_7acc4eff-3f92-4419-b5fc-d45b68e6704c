parameters:
    ignoreErrors:
        - message: '#Parameter \#2 \$message of function abort expects string, array\|string\|null given.#'
          path: '*.php'
          
        - message: '#Method .*::messages\(\) should return array<string, string> but returns array<string, array\|string\|null>.#'
          path: '*.php'

        - message: '#Call to an undefined method Illuminate\\Database\\Schema\\IndexDefinition::where\(\)#'
          path: 'database/migrations/*.php'
          
        - message: '#Class Spatie\\MediaLibraryPro\\Models\\TemporaryUpload not found#'
          path: 'config/media-library.php'

        - message: '#Class Spatie\\LaravelData\\Data not found#'
          path: 'config/settings.php'

        - message: '#PHPDoc tag @mixin contains unknown class Eloquent.#'
          path: '*'

        - message: '#Call to an undefined method Illuminate\\Database\\Eloquent\\Builder#'
          path: '*'

        - message: '#Call to an undefined method Illuminate\\Database\\Query\\Builder#'
          path: '*'

        - message: '#PHPDoc tag @mixin contains generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types: TModel#'
          path: '*'

        - message: '#Class App\\\\Models\\\\\\.* uses generic trait Illuminate\\\\Database\\\\Eloquent\\\\Factories\\\\HasFactory but does not specify its types: TFactory#'
          path: '*'

        - message: '#Cannot cast .* to string#'
          path: '*'

        -
          path: 'app/Providers/AppServiceProvider.php'
          identifier: offsetAccess.nonOffsetAccessible

        - message: '#Cannot cast .* to int#'
          path: '*'

        - message: '#Cannot cast array\|string to string#'
          path: '*'

        - message: '#Method App\\Models\\.*::slug\\(\\) return type with generic class Illuminate\\Database\\Eloquent\\Relations\\HasOne does not specify its types: TRelatedModel, TDeclaringModel#'
          path: src/Models/*.php

        - message: '#Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany<.*> in PHPDoc tag @return does not specify all template types of class#'
          path: app/Models/*.php

        - message: '#Call to an undefined static method App\\\\Models\\\\\\.*::factory\(\)#'
          path: 'database/factories/*.php'

        -
          identifier: missingType.generics

        - message: '#Call to an undefined method Illuminate\\Database\\Eloquent\\Relations\\HasMany<.*>::(published|scheduled)\(\)#'
          path: '*.php'

        - message: '#Call to an undefined method .*::(active|limit|first|firstOrFail|pluck|inRandomOrder|getAttribute)\(\)#'
          path: '*.php'
          
        - message: '#Call to an undefined method Illuminate\\Contracts\\Auth\\Guard::(login|attempt|logout)\(\)#'
          path: '*.php'
          
        - message: '#Call to an undefined method Illuminate\\Contracts\\Auth\\Guard::.*\(\)#'
          path: 'routes/web.php'
          
        - message: '#Cannot call method .*\(\) on array\\|Illuminate\\Routing\\Route#'
          path: 'routes/web.php'

        - message: '#PHPDoc tag @property-read for property .* contains unknown class Eloquent.#'
          path: '*.php'

        -
          identifier: staticMethod.notFound
