<?php

namespace App\Http\Controllers;

use App\Models\Template;
use App\Models\TemplateCategory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class TemplateController extends Controller
{
    /**
     * Display a listing of the templates.
     */
    public function index(Request $request): View
    {
        $query = Template::with('category')
            ->when($request->filled('search'), function ($q) use ($request) {
                $search = '%'.$request->input('search').'%';

                return $q->where('name', 'like', $search)
                    ->orWhere('description', 'like', $search);
            })
            ->when($request->filled('category') || $request->route('category_slug'), function ($q) use ($request) {
                $categorySlug = $request->route('category_slug') ?? $request->input('category');

                return $q->whereHas('category', function ($query) use ($categorySlug) {
                    $query->where('slug', $categorySlug);
                });
            })
            ->when($request->filled('features'), function ($q) use ($request) {
                $features = is_array($request->input('features'))
                    ? $request->input('features')
                    : [$request->input('features')];

                foreach ($features as $feature) {
                    $q->whereJsonContains('features', $feature);
                }

                return $q;
            })
            ->when($request->filled('price_min'), function ($q) use ($request) {
                $priceMin = (float) $request->input('price_min');
                return $q->where(function ($query) use ($priceMin) {
                    $query->where('sale_price', '>=', $priceMin)
                          ->orWhere(function ($q) use ($priceMin) {
                              $q->whereNull('sale_price')
                                ->where('original_price', '>=', $priceMin);
                          });
                });
            })
            ->when($request->filled('price_max'), function ($q) use ($request) {
                $priceMax = (float) $request->input('price_max');
                return $q->where(function ($query) use ($priceMax) {
                    $query->where('sale_price', '<=', $priceMax)
                          ->orWhere(function ($q) use ($priceMax) {
                              $q->whereNull('sale_price')
                                ->where('original_price', '<=', $priceMax);
                          });
                });
            })
            ->when($request->filled('is_featured'), function ($q) use ($request) {
                return $q->where('is_featured', (bool) $request->input('is_featured'));
            })
            ->when($request->filled('has_sale'), function ($q) use ($request) {
                if ($request->input('has_sale')) {
                    return $q->whereNotNull('sale_price')
                             ->whereColumn('sale_price', '<', 'original_price');
                }
                return $q;
            });

        // Handle sorting
        switch ($request->get('sort', 'newest')) {
            case 'popular':
                $query->orderBy('view_count', 'desc');
                break;
            case 'price_low':
                $query->orderByRaw('COALESCE(sale_price, original_price) ASC');
                break;
            case 'price_high':
                $query->orderByRaw('COALESCE(sale_price, original_price) DESC');
                break;
            case 'featured':
                $query->where('is_featured', true)->latest();
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            default:
                $query->latest();
        }

        $templates = $query->paginate(12)->appends($request->query());
        $categories = TemplateCategory::where('is_active', true)->withCount('templates')->get();

        // Get all unique features for filter
        /** @var Collection<int, string> $allFeatures */
        $allFeatures = Template::query()
            ->whereNotNull('features')
            ->pluck('features')
            ->filter()
            ->flatMap(fn ($features) => is_array($features) ? $features : [])
            ->filter()
            ->unique()
            ->sort()
            ->values();

        // Get price range for filters
        $priceRange = Template::query()
            ->selectRaw('
                MIN(COALESCE(sale_price, original_price)) as min_price,
                MAX(COALESCE(sale_price, original_price)) as max_price
            ')
            ->first();

        return view('pages.templates.index', [
            'templates' => $templates,
            'categories' => $categories,
            'allFeatures' => $allFeatures,
            'priceRange' => $priceRange,
            'currentFilters' => [
                'search' => $request->input('search'),
                'category' => $request->input('category'),
                'features' => $request->input('features', []),
                'price_min' => $request->input('price_min'),
                'price_max' => $request->input('price_max'),
                'is_featured' => $request->input('is_featured'),
                'has_sale' => $request->input('has_sale'),
                'sort' => $request->input('sort', 'newest'),
            ],
        ]);
    }

    /**
     * Display the specified template.
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function show(string $slug): View
    {
        /** @var Template $template */
        $template = Template::query()->with('category')
            ->where('slug', $slug)
            ->firstOrFail();

        // Increment view count
        $template->incrementViewCount();

        // Get related templates from the same category
        $relatedTemplates = Template::where('category_id', $template->category_id)
            ->where('id', '!=', $template->id)
            ->inRandomOrder()
            ->limit(8)
            ->get();

        // Get random templates for "You may also like" section
        $randomTemplates = Template::where('id', '!=', $template->id)
            ->inRandomOrder()
            ->limit(8)
            ->get();

        return view('pages.templates.show', [
            'template' => $template,
            'relatedTemplates' => $relatedTemplates,
            'randomTemplates' => $randomTemplates,
        ]);
    }
}
