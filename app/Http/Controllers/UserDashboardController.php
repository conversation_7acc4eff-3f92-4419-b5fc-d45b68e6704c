<?php

namespace App\Http\Controllers;

use App\Http\Requests\Dashboard\DashboardUpdateCompanyRequest;
use App\Http\Requests\Dashboard\DashboardUpdateUserRequest;
use App\Mail\Auth\AuthEmailChangedMail;
use App\Mail\Auth\AuthPasswordChangedMail;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class UserDashboardController extends Controller
{
    public function index(Request $request): View|RedirectResponse
    {
        try {
            // Get the current user from customer_users table
            $user = Auth::guard('customer_user')->user();
            if (! $user instanceof \App\Models\CustomerUser) {
                return redirect()->route('login')
                    ->withErrors(['auth' => __('messages.auth.invalid_session')])
                    ->with('error', __('messages.auth.login_required'));
            }

            // Get company info using model relation
            $company = $user->customer ?? null;
            if (! $company) {
                return redirect()->route('home')
                    ->withErrors(['company' => __('messages.company.not_linked')])
                    ->with('error', __('messages.company.contact_support'));
            }

            // Get websites for the user (including demos)
            $websites = $user->websites()->orderBy('created_at', 'desc')->get();

            return view('pages.user-dashboard.index', compact('user', 'company', 'websites'));
        } catch (\Exception $e) {
            Log::error('Error loading user details page', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->route('home')
                ->with('error', __('messages.errors.loading'));
        }
    }

    /**
     * @throws \Throwable
     */
    public function updateCompany(DashboardUpdateCompanyRequest $request): RedirectResponse
    {
        try {
            // Get user and company using Eloquent
            $user = Auth::guard('customer_user')->user();
            if (! $user instanceof \App\Models\CustomerUser || ! isset($user->customer_id)) {
                return redirect()->back()
                    ->withErrors(['auth' => __('messages.auth.invalid_session')])
                    ->with('error', __('messages.auth.authentication_failed'));
            }

            $company = $user->customer;
            if (! $company) {
                return redirect()->back()
                    ->withErrors(['company' => __('messages.company.not_found')])
                    ->with('error', __('messages.company.contact_support'));
            }

            // Validate input using Form Request (already validated by UpdateCompanyRequest)
            $validated = $request->validated();

            // Use DB transaction for update
            DB::beginTransaction();

            $oldEmail = $company->email;
            $emailChanged = $oldEmail !== $validated['company_email'];

            $company->fill([
                'business_name' => $validated['company_name'],
                'tax_code' => $validated['company_tax'],
                'contact_person' => $validated['company_contact'],
                'phone' => $validated['company_phone'],
                'email' => $validated['company_email'],
                'address' => $validated['company_address'],
                'city' => $validated['company_city'],
                'country' => $validated['company_country'],
                'website' => $validated['company_website'],
            ]);

            // If email changed, mark as unverified
            if ($emailChanged) {
                $company->email_verified_at = null;
            }

            $company->save();
            DB::commit();

            // Send email verification if email changed
            if ($emailChanged && $company->email) {
                $company->sendEmailVerificationNotification();

                return redirect()->back()
                    ->with('success', __('messages.company.update_success'))
                    ->with('info', __('messages.email.customer_verification_sent', ['email' => $company->email]));
            }

            return redirect()->back()->with('success', __('messages.company.update_success'));

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Unexpected error updating company information', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->back()
                ->withErrors(['general' => __('messages.errors.general')])
                ->with('error', __('messages.company.update_failed'));
        }
    }

    /**
     * @throws \Throwable
     */
    public function update(DashboardUpdateUserRequest $request): RedirectResponse
    {
        try {
            // Get user using Eloquent
            $user = Auth::guard('customer_user')->user();
            if (! $user instanceof \App\Models\CustomerUser) {
                return redirect()->back()
                    ->withErrors(['auth' => __('messages.auth.invalid_session')])
                    ->with('error', __('messages.user.login_again'));
            }

            // Use DB transaction for update
            DB::beginTransaction();

            $oldEmail = $user->email;
            $emailChanged = $oldEmail !== $request->validated('email');
            $passwordChanged = $request->has('password') && ! empty($request->validated('password'));

            $user->fill([
                'name' => $request->validated('name'),
                'email' => $request->validated('email'),
            ]);

            // Only update password if provided
            if ($passwordChanged) {
                $user->password = Hash::make($request->validated('password'));
            }

            // If email changed, mark as unverified
            if ($emailChanged) {
                $user->email_verified_at = null;
            }

            $user->save();
            DB::commit();

            // Send email notifications
            if ($emailChanged) {
                Mail::to($oldEmail)->send(new AuthEmailChangedMail($user, $oldEmail));
                // Send verification email to new email
                event(new Registered($user));
            }

            if ($passwordChanged) {
                Mail::to($user->email)->send(new AuthPasswordChangedMail($user));
            }

            return redirect()->back()->with('success', __('messages.user.update_success'));

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Unexpected error updating user information', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->back()
                ->withErrors(['general' => __('messages.errors.general')])
                ->with('error', __('messages.user.update_failed'));
        }
    }
}
