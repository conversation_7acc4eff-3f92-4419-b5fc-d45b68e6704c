<?php

namespace App\Http\Requests\Auth;

use App\Rules\EmailStrictRule;
use App\Rules\PhoneNumberRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules;

class AuthRegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, array<mixed>|\Illuminate\Contracts\Validation\ValidationRule|string>
     */
    public function rules(): array
    {
        return [
            'account_type' => ['required', 'in:business,individual'],
            'contact_person' => ['required', 'string', 'max:255'],
            'business_name' => ['required_if:account_type,business', 'string', 'max:255'],
            'email' => ['required', 'string', 'max:255', 'unique:customer_users,email', new EmailStrictRule],
            'phone' => ['required', 'string', 'max:20', new PhoneNumberRule],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'email.unique' => __('messages.validation.email_unique'),
            'email.*' => __('messages.validation.email_strict'),
        ];
    }
}
