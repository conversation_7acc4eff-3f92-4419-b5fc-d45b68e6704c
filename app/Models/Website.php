<?php

namespace App\Models;

use App\Enums\WebsitePackageEnum;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $customer_user_id
 * @property string $name
 * @property string $phone
 * @property string $company
 * @property string $domain
 * @property WebsitePackageEnum $package Gói đang sử dụng
 * @property null|string $purpose
 * @property null|string $detail
 * @property string $status
 * @property null|int $template_id
 * @property null|string $template_name
 * @property null|string $note
 * @property null|string $admin_username
 * @property null|string $admin_password
 * @property null|string $customer_username
 * @property null|string $customer_password
 * @property null|string $deploy_logs
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property null|string $deleted_at
 * @property-read null|Template $template
 * @property-read null|CustomerUser $customerUser
 *
 * @method static Builder<static>|Website newModelQuery()
 * @method static Builder<static>|Website newQuery()
 * @method static Builder<static>|Website query()
 * @method static Builder<static>|Website whereAdminPassword($value)
 * @method static Builder<static>|Website whereAdminUsername($value)
 * @method static Builder<static>|Website whereCompany($value)
 * @method static Builder<static>|Website whereCreatedAt($value)
 * @method static Builder<static>|Website whereCustomerPassword($value)
 * @method static Builder<static>|Website whereCustomerUsername($value)
 * @method static Builder<static>|Website whereDeletedAt($value)
 * @method static Builder<static>|Website whereDeployLogs($value)
 * @method static Builder<static>|Website whereDetail($value)
 * @method static Builder<static>|Website whereDomain($value)
 * @method static Builder<static>|Website whereId($value)
 * @method static Builder<static>|Website whereName($value)
 * @method static Builder<static>|Website whereNote($value)
 * @method static Builder<static>|Website wherePackage($value)
 * @method static Builder<static>|Website wherePhone($value)
 * @method static Builder<static>|Website wherePurpose($value)
 * @method static Builder<static>|Website whereStatus($value)
 * @method static Builder<static>|Website whereTemplateId($value)
 * @method static Builder<static>|Website whereTemplateName($value)
 * @method static Builder<static>|Website whereUpdatedAt($value)
 * @method static Builder<static>|Website whereCustomerUserId($value)
 *
 * @mixin Builder
 * @mixin \Eloquent
 */
class Website extends Model
{
    public const STATUS_PENDING = 'pending';

    public const STATUS_ACTIVE = 'active';

    public const STATUS_INACTIVE = 'inactive';

    protected $fillable = [
        'customer_user_id',
        'name',
        'phone',
        'company',
        'purpose',
        'domain',
        'package',
        'detail',
        'status',
        'template_id',
        'template_name',
        'note',
        'admin_username',
        'admin_password',
        'customer_username',
        'customer_password',
        'deploy_logs',
    ];

    protected $casts = [
        'package' => WebsitePackageEnum::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the customer user that owns the website.
     *
     * @return BelongsTo<CustomerUser, $this>
     */
    public function customerUser(): BelongsTo
    {
        return $this->belongsTo(CustomerUser::class);
    }

    /**
     * Get the template that the website is using.
     *
     * @return BelongsTo<Template, $this>
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }

    /**
     * Get the subscriptions for the website.
     *
     * @return HasMany<Subscription>
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the active subscription for the website.
     */
    public function activeSubscription(): ?Subscription
    {
        return $this->subscriptions()
            ->where('status', Subscription::STATUS_ACTIVE)
            ->where('ends_at', '>', now())
            ->first();
    }

    /**
     * Get package display name
     */
    public function getPackageDisplayName(): string
    {
        return $this->package->label();
    }

    /**
     * Get package features
     */
    public function getPackageFeatures(): array
    {
        return $this->package->features();
    }

    /**
     * Check if website has specific feature
     */
    public function hasFeature(string $feature): bool
    {
        return $this->package->hasFeature($feature);
    }

    /**
     * Get formatted package price
     */
    public function getPackagePrice(): string
    {
        return $this->package->formattedPrice();
    }

    /**
     * Check if website can be upgraded
     */
    public function canUpgrade(): bool
    {
        return !empty($this->package->getUpgradeOptions());
    }

    /**
     * Get upgrade options
     */
    public function getUpgradeOptions(): array
    {
        return $this->package->getUpgradeOptions();
    }
}
