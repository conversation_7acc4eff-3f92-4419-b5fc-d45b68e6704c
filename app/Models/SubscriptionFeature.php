<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $subscription_id
 * @property string $feature_name
 * @property string $feature_value
 * @property bool $is_enabled
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Subscription $subscription
 */
class SubscriptionFeature extends Model
{
    use HasFactory;

    protected $fillable = [
        'subscription_id',
        'feature_name',
        'feature_value',
        'is_enabled',
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
    ];

    /**
     * Get the subscription that owns the feature.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get feature display name.
     */
    public function getDisplayName(): string
    {
        $names = [
            'storage_gb' => 'Storage (GB)',
            'bandwidth_gb' => 'Bandwidth (GB)',
            'custom_domain' => 'Custom Domain',
            'ssl_certificate' => 'SSL Certificate',
            'email_accounts' => 'Email Accounts',
            'support_level' => 'Support Level',
            'backup_frequency' => 'Backup Frequency',
            'dedicated_server' => 'Dedicated Server',
        ];

        return $names[$this->feature_name] ?? $this->feature_name;
    }

    /**
     * Get formatted feature value.
     */
    public function getFormattedValue(): string
    {
        switch ($this->feature_name) {
            case 'storage_gb':
            case 'bandwidth_gb':
                return $this->feature_value . ' GB';
            case 'custom_domain':
            case 'ssl_certificate':
            case 'dedicated_server':
                return $this->feature_value === 'true' ? 'Yes' : 'No';
            case 'email_accounts':
                return $this->feature_value === 'unlimited' ? 'Unlimited' : $this->feature_value;
            default:
                return $this->feature_value;
        }
    }
}
