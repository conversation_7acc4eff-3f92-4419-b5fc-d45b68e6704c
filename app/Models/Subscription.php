<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $website_id
 * @property string $plan_name
 * @property string $plan_type
 * @property float $price
 * @property string $currency
 * @property string $status
 * @property Carbon $starts_at
 * @property Carbon $ends_at
 * @property bool $auto_renew
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Website $website
 * @property-read \Illuminate\Database\Eloquent\Collection<int, SubscriptionFeature> $features
 */
class Subscription extends Model
{
    use HasFactory;

    // Plan Names
    public const PLAN_BASIC = 'basic';
    public const PLAN_PRO = 'pro';
    public const PLAN_ENTERPRISE = 'enterprise';

    // Plan Types
    public const TYPE_MONTHLY = 'monthly';
    public const TYPE_YEARLY = 'yearly';

    // Status
    public const STATUS_ACTIVE = 'active';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_EXPIRED = 'expired';
    public const STATUS_PENDING = 'pending';

    protected $fillable = [
        'website_id',
        'plan_name',
        'plan_type',
        'price',
        'currency',
        'status',
        'starts_at',
        'ends_at',
        'auto_renew',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'auto_renew' => 'boolean',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the website that owns the subscription.
     */
    public function website(): BelongsTo
    {
        return $this->belongsTo(Website::class);
    }

    /**
     * Get the features for the subscription.
     */
    public function features(): HasMany
    {
        return $this->hasMany(SubscriptionFeature::class);
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE && 
               $this->ends_at > now();
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->ends_at <= now();
    }

    /**
     * Get plan display name.
     */
    public function getPlanDisplayName(): string
    {
        $plans = [
            self::PLAN_BASIC => 'Basic Plan',
            self::PLAN_PRO => 'Pro Plan',
            self::PLAN_ENTERPRISE => 'Enterprise Plan',
        ];

        return $plans[$this->plan_name] ?? $this->plan_name;
    }

    /**
     * Get plan features configuration.
     */
    public static function getPlanFeatures(string $planName): array
    {
        $features = [
            self::PLAN_BASIC => [
                'storage_gb' => '5',
                'bandwidth_gb' => '50',
                'custom_domain' => 'false',
                'ssl_certificate' => 'true',
                'email_accounts' => '5',
                'support_level' => 'basic',
            ],
            self::PLAN_PRO => [
                'storage_gb' => '20',
                'bandwidth_gb' => '200',
                'custom_domain' => 'true',
                'ssl_certificate' => 'true',
                'email_accounts' => '20',
                'support_level' => 'priority',
                'backup_frequency' => 'daily',
            ],
            self::PLAN_ENTERPRISE => [
                'storage_gb' => '100',
                'bandwidth_gb' => '1000',
                'custom_domain' => 'true',
                'ssl_certificate' => 'true',
                'email_accounts' => 'unlimited',
                'support_level' => '24/7',
                'backup_frequency' => 'hourly',
                'dedicated_server' => 'true',
            ],
        ];

        return $features[$planName] ?? [];
    }

    /**
     * Create subscription with features.
     */
    public static function createWithFeatures(array $data): self
    {
        $subscription = self::create($data);
        
        $features = self::getPlanFeatures($data['plan_name']);
        foreach ($features as $name => $value) {
            $subscription->features()->create([
                'feature_name' => $name,
                'feature_value' => $value,
                'is_enabled' => true,
            ]);
        }

        return $subscription;
    }
}
