<?php

namespace App\Enums;

enum WebsiteStatusEnum: string
{
    case DEMO = 'demo';
    case TRIAL = 'trial';
    case ACTIVE = 'active';
    case SUSPENDED = 'suspended';
    case CANCELLED = 'cancelled';
    case PENDING = 'pending';
    case INACTIVE = 'inactive';

    /**
     * @return array<string>
     */
    public static function values(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    /**
     * Get status label
     */
    public function label(): string
    {
        return match ($this) {
            self::DEMO => (string) __('enums.website.status.demo'),
            self::TRIAL => (string) __('enums.website.status.trial'),
            self::ACTIVE => (string) __('enums.website.status.active'),
            self::SUSPENDED => (string) __('enums.website.status.suspended'),
            self::CANCELLED => (string) __('enums.website.status.cancelled'),
            self::PENDING => (string) __('enums.website.status.pending'),
            self::INACTIVE => (string) __('enums.website.status.inactive'),
        };
    }

    /**
     * Get status color for UI
     */
    public function color(): string
    {
        return match ($this) {
            self::DEMO => 'blue',
            self::TRIAL => 'yellow',
            self::ACTIVE => 'green',
            self::SUSPENDED => 'red',
            self::CANCELLED => 'gray',
            self::PENDING => 'orange',
            self::INACTIVE => 'gray',
        };
    }

    /**
     * Get CSS classes for status badge
     */
    public function badgeClasses(): string
    {
        return match ($this) {
            self::DEMO => 'bg-blue-100 text-blue-800',
            self::TRIAL => 'bg-yellow-100 text-yellow-800',
            self::ACTIVE => 'bg-green-100 text-green-800',
            self::SUSPENDED => 'bg-red-100 text-red-800',
            self::CANCELLED => 'bg-gray-100 text-gray-800',
            self::PENDING => 'bg-orange-100 text-orange-800',
            self::INACTIVE => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Check if status allows conversion to trial
     */
    public function canConvertToTrial(): bool
    {
        return $this === self::DEMO;
    }

    /**
     * Check if status allows conversion to production
     */
    public function canConvertToProduction(): bool
    {
        return in_array($this, [self::DEMO, self::TRIAL]);
    }

    /**
     * Check if website is active
     */
    public function isActive(): bool
    {
        return in_array($this, [self::DEMO, self::TRIAL, self::ACTIVE]);
    }
}
