<?php

namespace App\Enums;

enum WebsiteLifecycleEnum: string
{
    case DEMO = 'demo';
    case TRIAL = 'trial';
    case PRODUCTION = 'production';

    /**
     * Get all lifecycle values
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get lifecycle label
     */
    public function label(): string
    {
        return match ($this) {
            self::DEMO => __('messages.website.lifecycle_demo'),
            self::TRIAL => __('messages.website.lifecycle_trial'),
            self::PRODUCTION => __('messages.website.lifecycle_production'),
        };
    }

    /**
     * Get lifecycle color for UI
     */
    public function color(): string
    {
        return match ($this) {
            self::DEMO => 'blue',
            self::TRIAL => 'yellow',
            self::PRODUCTION => 'green',
        };
    }

    /**
     * Get CSS classes for lifecycle badge
     */
    public function badgeClasses(): string
    {
        return match ($this) {
            self::DEMO => 'bg-blue-100 text-blue-800',
            self::TRIAL => 'bg-yellow-100 text-yellow-800',
            self::PRODUCTION => 'bg-green-100 text-green-800',
        };
    }

    /**
     * Get default expiry days
     */
    public function defaultExpiryDays(): ?int
    {
        return match ($this) {
            self::DEMO => 30,
            self::TRIAL => 14,
            self::PRODUCTION => null,
        };
    }

    /**
     * Check if lifecycle is temporary
     */
    public function isTemporary(): bool
    {
        return in_array($this, [self::DEMO, self::TRIAL]);
    }
}
