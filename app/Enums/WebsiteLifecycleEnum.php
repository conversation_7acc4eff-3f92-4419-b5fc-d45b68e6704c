<?php

namespace App\Enums;

enum WebsiteLifecycleEnum: string
{
    case Demo = 'demo';
    case Trial = 'trial';
    case Production = 'production';

    /**
     * @return array<string>
     */
    public static function values(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public function label(): string
    {
        return match ($this) {
            self::Demo => (string) __('enums.website.lifecycle.demo'),
            self::Trial => (string) __('enums.website.lifecycle.trial'),
            self::Production => (string) __('enums.website.lifecycle.production'),
        };
    }
}
