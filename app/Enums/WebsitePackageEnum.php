<?php

namespace App\Enums;

enum WebsitePackageEnum: string
{
    case Basic = 'basic';
    case Standard = 'standard';
    case Premium = 'premium';
    case Enterprise = 'enterprise';

    /**
     * @return array<string>
     */
    public static function values(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public function label(): string
    {
        return match ($this) {
            self::Basic => (string) __('enums.website.package.basic'),
            self::Standard => (string) __('enums.website.package.standard'),
            self::Premium => (string) __('enums.website.package.premium'),
            self::Enterprise => (string) __('enums.website.package.enterprise'),
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::Basic => (string) __('enums.website.package.basic_desc'),
            self::Standard => (string) __('enums.website.package.standard_desc'),
            self::Premium => (string) __('enums.website.package.premium_desc'),
            self::Enterprise => (string) __('enums.website.package.enterprise_desc'),
        };
    }

    public function formattedPrice(): string
    {
        return match ($this) {
            self::Basic => (string) __('enums.website.package.basic_price'),
            self::Standard => (string) __('enums.website.package.standard_price'),
            self::Premium => (string) __('enums.website.package.premium_price'),
            self::Enterprise => (string) __('enums.website.package.enterprise_price'),
        };
    }

    /**
     * @return array<string>
     */
    public function features(): array
    {
        return match ($this) {
            self::Basic => [
                (string) __('enums.website.package.feature.responsive'),
                (string) __('enums.website.package.feature.seo'),
                (string) __('enums.website.package.feature.basic_support'),
                (string) __('enums.website.package.feature.basic_hosting'),
            ],
            self::Standard => [
                (string) __('enums.website.package.feature.responsive'),
                (string) __('enums.website.package.feature.seo'),
                (string) __('enums.website.package.feature.standard_support'),
                (string) __('enums.website.package.feature.standard_hosting'),
                (string) __('enums.website.package.feature.ssl'),
                (string) __('enums.website.package.feature.analytics'),
            ],
            self::Premium => [
                (string) __('enums.website.package.feature.responsive'),
                (string) __('enums.website.package.feature.seo'),
                (string) __('enums.website.package.feature.premium_support'),
                (string) __('enums.website.package.feature.premium_hosting'),
                (string) __('enums.website.package.feature.ssl'),
                (string) __('enums.website.package.feature.analytics'),
                (string) __('enums.website.package.feature.custom_domain'),
                (string) __('enums.website.package.feature.backup'),
            ],
            self::Enterprise => [
                (string) __('enums.website.package.feature.responsive'),
                (string) __('enums.website.package.feature.seo'),
                (string) __('enums.website.package.feature.enterprise_support'),
                (string) __('enums.website.package.feature.enterprise_hosting'),
                (string) __('enums.website.package.feature.ssl'),
                (string) __('enums.website.package.feature.analytics'),
                (string) __('enums.website.package.feature.custom_domain'),
                (string) __('enums.website.package.feature.backup'),
                (string) __('enums.website.package.feature.priority_support'),
                (string) __('enums.website.package.feature.custom_features'),
            ],
        };
    }

    /**
     * @return array<string, WebsitePackageEnum>
     */
    public function getUpgradeOptions(): array
    {
        return match ($this) {
            self::Basic => [
                'standard' => self::Standard,
                'premium' => self::Premium,
                'enterprise' => self::Enterprise,
            ],
            self::Standard => [
                'premium' => self::Premium,
                'enterprise' => self::Enterprise,
            ],
            self::Premium => [
                'enterprise' => self::Enterprise,
            ],
            self::Enterprise => [],
        };
    }
}
