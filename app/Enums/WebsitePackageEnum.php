<?php

namespace App\Enums;

enum WebsitePackageEnum: string
{
    case EXPRESS_BASIC = 'express_basic';
    case EXPRESS_PLUS = 'express_plus';
    case PRO_BASIC = 'pro_basic';
    case PRO_PLUS = 'pro_plus';
    case ENTERPRISE = 'enterprise';
    case CUSTOM = 'custom';

    /**
     * Get all package values
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get package display name
     */
    public function label(): string
    {
        return match ($this) {
            self::EXPRESS_BASIC => __('messages.package.express_basic'),
            self::EXPRESS_PLUS => __('messages.package.express_plus'),
            self::PRO_BASIC => __('messages.package.pro_basic'),
            self::PRO_PLUS => __('messages.package.pro_plus'),
            self::ENTERPRISE => __('messages.package.enterprise'),
            self::CUSTOM => __('messages.package.custom'),
        };
    }

    /**
     * Get package description
     */
    public function description(): string
    {
        return match ($this) {
            self::EXPRESS_BASIC => __('messages.package.express_basic_desc'),
            self::EXPRESS_PLUS => __('messages.package.express_plus_desc'),
            self::PRO_BASIC => __('messages.package.pro_basic_desc'),
            self::PRO_PLUS => __('messages.package.pro_plus_desc'),
            self::ENTERPRISE => __('messages.package.enterprise_desc'),
            self::CUSTOM => __('messages.package.custom_desc'),
        };
    }

    /**
     * Get package price (VND)
     */
    public function price(): int
    {
        return match ($this) {
            self::EXPRESS_BASIC => 1500000,
            self::EXPRESS_PLUS => 2500000,
            self::PRO_BASIC => 3500000,
            self::PRO_PLUS => 5000000,
            self::ENTERPRISE => 8000000,
            self::CUSTOM => 0, // Custom pricing
        };
    }

    /**
     * Get formatted price
     */
    public function formattedPrice(): string
    {
        if ($this === self::CUSTOM) {
            return __('messages.package.contact_for_price');
        }
        
        return number_format($this->price(), 0, ',', '.') . 'đ';
    }

    /**
     * Get package features
     */
    public function features(): array
    {
        return match ($this) {
            self::EXPRESS_BASIC => [
                'storage' => '5GB',
                'bandwidth' => '50GB/month',
                'domains' => '1',
                'email_accounts' => '5',
                'ssl' => true,
                'support' => 'basic',
                'backup' => 'weekly',
                'custom_domain' => false,
            ],
            self::EXPRESS_PLUS => [
                'storage' => '10GB',
                'bandwidth' => '100GB/month',
                'domains' => '3',
                'email_accounts' => '10',
                'ssl' => true,
                'support' => 'priority',
                'backup' => 'daily',
                'custom_domain' => true,
            ],
            self::PRO_BASIC => [
                'storage' => '20GB',
                'bandwidth' => '200GB/month',
                'domains' => '5',
                'email_accounts' => '20',
                'ssl' => true,
                'support' => 'priority',
                'backup' => 'daily',
                'custom_domain' => true,
                'ecommerce' => true,
            ],
            self::PRO_PLUS => [
                'storage' => '50GB',
                'bandwidth' => '500GB/month',
                'domains' => '10',
                'email_accounts' => '50',
                'ssl' => true,
                'support' => '24/7',
                'backup' => 'daily',
                'custom_domain' => true,
                'ecommerce' => true,
                'advanced_seo' => true,
            ],
            self::ENTERPRISE => [
                'storage' => '100GB',
                'bandwidth' => '1TB/month',
                'domains' => 'unlimited',
                'email_accounts' => 'unlimited',
                'ssl' => true,
                'support' => '24/7',
                'backup' => 'hourly',
                'custom_domain' => true,
                'ecommerce' => true,
                'advanced_seo' => true,
                'dedicated_server' => true,
                'white_label' => true,
            ],
            self::CUSTOM => [],
        };
    }

    /**
     * Get package color for UI
     */
    public function color(): string
    {
        return match ($this) {
            self::EXPRESS_BASIC => 'blue',
            self::EXPRESS_PLUS => 'green',
            self::PRO_BASIC => 'purple',
            self::PRO_PLUS => 'orange',
            self::ENTERPRISE => 'red',
            self::CUSTOM => 'gray',
        };
    }

    /**
     * Get CSS classes for package badge
     */
    public function badgeClasses(): string
    {
        return match ($this) {
            self::EXPRESS_BASIC => 'bg-blue-100 text-blue-800',
            self::EXPRESS_PLUS => 'bg-green-100 text-green-800',
            self::PRO_BASIC => 'bg-purple-100 text-purple-800',
            self::PRO_PLUS => 'bg-orange-100 text-orange-800',
            self::ENTERPRISE => 'bg-red-100 text-red-800',
            self::CUSTOM => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Check if package supports feature
     */
    public function hasFeature(string $feature): bool
    {
        $features = $this->features();
        return isset($features[$feature]) && $features[$feature] !== false;
    }

    /**
     * Get feature value
     */
    public function getFeature(string $feature): mixed
    {
        $features = $this->features();
        return $features[$feature] ?? null;
    }

    /**
     * Check if package is premium
     */
    public function isPremium(): bool
    {
        return in_array($this, [self::PRO_BASIC, self::PRO_PLUS, self::ENTERPRISE]);
    }

    /**
     * Check if package is enterprise level
     */
    public function isEnterprise(): bool
    {
        return $this === self::ENTERPRISE;
    }

    /**
     * Get recommended packages for upgrade
     */
    public function getUpgradeOptions(): array
    {
        return match ($this) {
            self::EXPRESS_BASIC => [self::EXPRESS_PLUS, self::PRO_BASIC],
            self::EXPRESS_PLUS => [self::PRO_BASIC, self::PRO_PLUS],
            self::PRO_BASIC => [self::PRO_PLUS, self::ENTERPRISE],
            self::PRO_PLUS => [self::ENTERPRISE],
            self::ENTERPRISE => [],
            self::CUSTOM => [],
        };
    }
}
