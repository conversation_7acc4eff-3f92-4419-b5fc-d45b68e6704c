<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('websites', function (Blueprint $table) {
            // Update status enum to include new values
            $table->enum('status', ['demo', 'trial', 'active', 'suspended', 'cancelled'])->default('demo')->change();

            // Add lifecycle stage
            $table->enum('lifecycle_stage', ['demo', 'trial', 'production'])->default('demo')->after('status');

            // Add subscription relationship
            $table->foreignId('subscription_id')->nullable()->after('lifecycle_stage');

            // Add expiry dates
            $table->timestamp('demo_expires_at')->nullable()->after('subscription_id');
            $table->timestamp('trial_expires_at')->nullable()->after('demo_expires_at');

            // Add demo content
            $table->text('demo_content')->nullable()->after('trial_expires_at');

            // Remove old is_demo column if exists
            if (Schema::hasColumn('websites', 'is_demo')) {
                $table->dropColumn('is_demo');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('websites', function (Blueprint $table) {
            $table->dropColumn([
                'lifecycle_stage',
                'subscription_id',
                'demo_expires_at',
                'trial_expires_at',
                'demo_content',
            ]);

            // Restore old status enum
            $table->enum('status', ['pending', 'active', 'inactive'])->default('pending')->change();

            // Add back is_demo if needed
            $table->boolean('is_demo')->default(true);
        });
    }
};
