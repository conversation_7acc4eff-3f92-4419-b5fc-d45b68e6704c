<?php

namespace Database\Factories;

use App\Models\Opportunity;
use App\Models\Quote;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class QuoteFactory extends Factory
{
    protected $model = Quote::class;

    public function definition(): array
    {
        return [
            'quote_number' => $this->faker->word(),
            'title' => $this->faker->word(),
            'description' => $this->faker->text(),
            'amount' => $this->faker->word(),
            'valid_until' => Carbon::now(),
            'status' => $this->faker->word(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'opportunity_id' => Opportunity::factory(),
            'created_by' => User::factory(),
        ];
    }
}
