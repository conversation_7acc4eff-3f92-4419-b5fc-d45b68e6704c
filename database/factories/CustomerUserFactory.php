<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\CustomerUser;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class CustomerUserFactory extends Factory
{
    protected $model = CustomerUser::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => bcrypt($this->faker->password()),
            'is_owner' => $this->faker->boolean(),
            'email_verified_at' => Carbon::now(),
            'status' => $this->faker->word(),

            'customer_id' => Customer::factory(),
        ];
    }
}
