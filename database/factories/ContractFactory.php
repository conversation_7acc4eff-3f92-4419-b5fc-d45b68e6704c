<?php

namespace Database\Factories;

use App\Models\Contract;
use App\Models\Customer;
use App\Models\Opportunity;
use App\Models\Quote;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ContractFactory extends Factory
{
    protected $model = Contract::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'contract_number' => 'CNT-'.$this->faker->unique()->numberBetween(1000, 9999),
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph,
            'amount' => $this->faker->randomFloat(2, 1000, 100000),
            'start_date' => $this->faker->dateTimeBetween('now', '+1 month')->format('Y-m-d'),
            'end_date' => $this->faker->dateTimeBetween('+2 months', '+1 year')->format('Y-m-d'),
            'status' => $this->faker->randomElement(['draft', 'sent', 'signed', 'active', 'expired', 'terminated']),
            'customer_id' => Customer::factory(),
            'opportunity_id' => Opportunity::factory(),
            'quote_id' => Quote::factory(),
            'created_by' => User::factory(),
            'signed_at' => $this->faker->optional(0.3)->dateTimeThisYear(),
            'payment_terms' => $this->faker->optional(0.7)->paragraph,
            'terms_and_conditions' => $this->faker->optional(0.5)->paragraphs(3, true),
        ];
    }
}
