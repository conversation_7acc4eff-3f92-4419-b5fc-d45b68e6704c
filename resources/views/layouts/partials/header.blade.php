@php
    use Illuminate\Support\Facades\Route;
    use App\Helpers\MenuHelper;

    function getUrl($item)
    {
        if (isset($item['route'])) {
            return route($item['route'], $item['params'] ?? []);
        }
        return $item['url'] ?? '#';
    }

    $menus = MenuHelper::updateTemplatesInMenu();
@endphp

<header id="main-header" class="backdrop-blur-md bg-white/70 shadow-md sticky top-0 z-40 transition-all duration-300"
        x-data="{ mobileMenuOpen: $store.mobileMenu }" @keydown.escape="mobileMenuOpen.close()"
        :class="{ 'overflow-hidden h-screen': mobileMenuOpen.open }">
    <nav class="container mx-auto px-4 lg:px-6">
        <!-- Main Navigation Bar -->
        <div class="flex justify-between items-center py-4">
            <!-- Logo -->
            <a href="{{ route('home') }}" class="flex-shrink-0">
                <img src="{{ asset('images/cslant-logo.png') }}" alt="CSlant Logo" class="h-8 md:h-10"
                    onerror="this.onerror=null;this.src='https://placehold.co/150x40/338146/FFFFFF?text=CSlant';">
            </a>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex items-center space-x-6 xl:space-x-8 flex-1 justify-center">
                <!-- Main Menu Items -->
                <div class="flex items-center space-x-6 xl:space-x-8">
                    @foreach($menus as $menu)
                        <div class="relative group">
                            @if ($menu['route'])
                                <a href="{{ getUrl($menu) }}"
                                   class="text-sm text-gray-600 hover:text-emerald-600 font-medium flex items-center transition-colors duration-200">
                                    {{ $menu['title'] }}
                                    @if (!empty($menu['items']))
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    @endif
                                </a>
                            @else
                                <button class="text-sm text-gray-600 hover:text-emerald-600 font-medium flex items-center transition-colors duration-200">
                                    {{ $menu['title'] }}
                                    @if (!empty($menu['items']))
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    @endif
                                </button>
                            @endif

                        @if (!empty($menu['items']))
                            @if ($menu['type'] === 'dropdown')
                                <div
                                    class="{{ $menu['class'] ?? '' }} dropdown-menu absolute left-0 mt-2 w-72 py-3 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                                    <div class="p-2 space-y-1">
                                        @foreach ($menu['items'] as $item)
                                            <a href="{{ getUrl($item) }}"
                                                class="dropdown-item flex items-center px-4 py-2.5 text-gray-700">
                                                <div
                                                    class="w-8 h-8 rounded-lg bg-emerald-50 flex items-center justify-center mr-3 flex-shrink-0">
                                                    <svg class="w-4 h-4 text-emerald-600" fill="none"
                                                        stroke="currentColor" viewBox="0 0 24 24"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        {!! $item['svg'] !!}
                                                    </svg>
                                                </div>
                                                <div class="min-w-0">
                                                    <div class="font-medium text-gray-900 flex items-center">
                                                        {{ $item['title'] }}
                                                        @if (isset($item['is_popular']) && $item['is_popular'])
                                                            <span
                                                                class="ml-2 bg-emerald-100 text-emerald-800 text-[10px] font-medium px-2 py-0.5 rounded-full">
                                                                {{ __('messages.home.popular_choice') }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                    @if (isset($item['subtitle']))
                                                        <div class="text-xs text-gray-500 truncate">
                                                            {{ $item['subtitle'] }}</div>
                                                    @endif
                                                </div>
                                            </a>
                                        @endforeach
                                    </div>
                                </div>
                            @elseif($menu['type'] === 'mega_menu')
                                <div
                                    class="dropdown-menu absolute -left-[300px] mt-2 w-[1000px] py-4 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 p-6">
                                    <div class="grid grid-cols-3 gap-6">
                                        @php
                                            $columns = [[], [], []];
                                            foreach ($menu['items'] as $section) {
                                                $columnIndex = isset($section['column_id'])
                                                    ? min($section['column_id'] - 1, 2)
                                                    : 0;
                                                $columns[$columnIndex][] = $section;
                                            }
                                        @endphp

                                        @foreach ($columns as $columnSections)
                                            <div class="space-y-6">
                                                @foreach ($columnSections as $section)
                                                    <div class="dropdown-section">
                                                        <h3
                                                            class="font-bold text-gray-800 mb-4 pb-2 border-b border-gray-100 flex items-center">
                                                            <div
                                                                class="w-8 h-8 rounded-lg bg-emerald-50 flex items-center justify-center mr-2">
                                                                <svg class="w-4 h-4 text-emerald-600" fill="none"
                                                                    stroke="currentColor" viewBox="0 0 24 24"
                                                                    xmlns="http://www.w3.org/2000/svg">
                                                                    {!! $section['svg'] !!}
                                                                </svg>
                                                            </div>
                                                            <span>{{ $section['group'] }}</span>
                                                        </h3>
                                                        <ul class="space-y-1.5">
                                                            @foreach ($section['items'] as $item)
                                                                <li>
                                                                    <a href="{{ getUrl($item) }}"
                                                                        class="dropdown-item flex items-center px-3 py-2.5 text-gray-700">
                                                                        <span
                                                                            class="w-1.5 h-1.5 rounded-full bg-emerald-400 mr-2.5 flex-shrink-0"></span>
                                                                        <span
                                                                            class="font-medium truncate">{{ $item['title'] }}</span>
                                                                        @if (isset($item['is_popular']) && $item['is_popular'])
                                                                            <span
                                                                                class="ml-2 bg-emerald-100 text-emerald-800 text-[10px] font-medium px-2 py-0.5 rounded-full">
                                                                                {{ __('messages.home.popular_choice') }}
                                                                            </span>
                                                                        @endif
                                                                    </a>
                                                                </li>
                                                            @endforeach
                                                        </ul>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        @endif
                    </div>
                @endforeach
            </div>
            </div>

            <!-- Desktop Search Bar -->
            <div class="hidden xl:block">
                <form class="relative w-64" action="{{ route('templates.index') }}">
                    <x-form.input name="search" type="text" :value="request('search')" placeholder="{{ __('messages.website.search_placeholder') }}" :label="null" />
                    <button
                        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-emerald-600 px-1 bg-gray-50 hover:scale-110 transition-all duration-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </form>
            </div>

            <!-- Right Navigation -->
            <div class="flex items-center space-x-2 md:space-x-4">
                <!-- Search Button for Mobile/Tablet -->
                <button class="xl:hidden p-2 text-gray-600 hover:text-emerald-600 hover:bg-gray-100 rounded-full transition-colors duration-200"
                        @click="$store.searchModal.open()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
                <!-- User Dropdown -->
                <div class="relative" x-data="{ open: false }" @click.away="open = false">
                    @if (Auth::guard('customer_user')->check())
                        <!-- User is logged in -->
                        <button @click="open = !open"
                            class="flex items-center space-x-2 text-gray-700 hover:text-emerald-600 focus:outline-none p-1 rounded-full hover:bg-gray-100 transition-colors duration-200">
                            <div class="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center">
                                <span class="text-emerald-700 font-medium text-sm">{{ substr(Auth::guard('customer_user')->user()->name, 0, 1) }}</span>
                            </div>
                        </button>
                    @else
                        <!-- User is not logged in -->
                        <button @click="open = !open"
                            class="p-2 text-gray-600 hover:text-emerald-600 focus:outline-none hover:bg-gray-100 rounded-full transition-colors duration-200">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </button>
                    @endif

                    <!-- User Dropdown Menu -->
                    <div x-show="open" x-transition:enter="transition ease-out duration-100 transform"
                        x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
                        x-transition:leave="transition ease-in duration-75 transform"
                        x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95"
                        class="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-xl py-2 z-50 ring-1 ring-black ring-opacity-5"
                        x-cloak style="display: none; min-width: 16rem;">
                        @if (Auth::guard('customer_user')->check())
                            <!-- User is logged in menu -->
                            <div class="px-4 py-3 border-b border-gray-100 bg-gray-50 rounded-t-lg">
                                <p class="text-sm font-medium text-gray-700">{{ __('messages.nav.account') }}</p>
                            </div>
                            <div class="py-1">
                                <a href="{{ route('user-dashboard') }}"
                                    class="flex items-center px-4 py-3 text-base text-gray-700 hover:bg-emerald-50 transition-all duration-150 group">
                                    <i
                                        class="fas fa-user-circle w-5 text-center text-emerald-600 group-hover:scale-110 transition-transform duration-200"></i>
                                    <span class="ml-3 group-hover:text-emerald-700 group-hover:font-medium">{{ __('messages.nav.dashboard') }}</span>
                                </a>
                                <a href="{{ route('user-dashboard.favorites') }}"
                                    class="flex items-center px-4 py-3 text-base text-gray-700 hover:bg-emerald-50 transition-all duration-150 group">
                                    <i
                                        class="fas fa-heart w-5 text-center text-emerald-600 group-hover:scale-110 transition-transform duration-200"></i>
                                    <span class="ml-3 group-hover:text-emerald-700 group-hover:font-medium">{{ __('messages.template.my_favorites') }}</span>
                                </a>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit"
                                        class="w-full text-left flex items-center px-4 py-3 text-base text-red-600 hover:bg-gray-50 transition-colors duration-150">
                                        <i class="fas fa-sign-out-alt w-5 text-center"></i>
                                        <span class="ml-3">{{ __('messages.nav.logout') }}</span>
                                    </button>
                                </form>
                            </div>
                        @else
                            <!-- Guest user menu -->
                            <div class="py-1">
                                <a href="{{ route('login') }}"
                                    class="flex items-center px-4 py-3 text-base text-gray-700 hover:bg-emerald-50 transition-all duration-150 group">
                                    <i
                                        class="fas fa-sign-in-alt w-5 text-center text-emerald-600 group-hover:scale-110 transition-transform duration-200"></i>
                                    <span class="ml-3 group-hover:text-emerald-700 group-hover:font-medium">{{ __('messages.nav.login') }}</span>
                                </a>
                                <a href="{{ route('register') }}"
                                    class="flex items-center px-4 py-3 text-base text-gray-700 hover:bg-emerald-50 transition-all duration-150 group">
                                    <i
                                        class="fas fa-user-plus w-5 text-center text-emerald-600 group-hover:scale-110 transition-transform duration-200"></i>
                                    <span class="ml-3 group-hover:text-emerald-700 group-hover:font-medium">{{ __('messages.nav.register') }}</span>
                                </a>
                            </div>
                        @endif
                        <div class="border-t border-gray-100 my-1"></div>
                        <div class="py-1">
                            <a href="#"
                                class="flex items-center px-4 py-3 text-base text-gray-700 hover:bg-emerald-50 transition-all duration-150 group">
                                <i
                                    class="fas fa-question-circle w-5 text-center text-emerald-600 group-hover:scale-110 transition-transform duration-200"></i>
                                <span class="ml-3 group-hover:text-emerald-700 group-hover:font-medium">{{ __('messages.nav.help_support') }}</span>
                            </a>
                        </div>
                    </div>
                </div>


                <!-- Request Quote Button -->
                <x-ui.button variant="primary"
                             class="px-4 py-2 md:px-3 md:py-1.5 lg:px-6 lg:py-2.5 hover:cursor-pointer hover:-translate-y-0.5 hover:shadow-md transition-all duration-200"
                             x-data
                             @click="$store.modal.open()">
                    {{ __('messages.nav.request_quote') }}
                </x-ui.button>
            </div>
        </div>

        <!-- Mobile Search Bar (Hidden by default, shown when search button clicked) -->
        <div class="xl:hidden border-t border-gray-100 px-4 py-3" x-show="$store.searchModal.open" x-transition>
            <form class="relative" action="{{ route('templates.index') }}">
                <x-form.input name="search" type="text" :value="request('search')" placeholder="{{ __('messages.website.search_placeholder') }}" :label="null" />
                <button type="submit"
                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-emerald-600 px-1 bg-gray-50 hover:scale-110 transition-all duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
            </form>
        </div>

        <!-- Mobile menu (Hidden by default) -->
        <div class="lg:hidden border-t border-gray-100" x-show="mobileMenuOpen.open" x-transition>
            <!-- Mobile menu content will be here -->
            <div class="px-4 py-4 space-y-4">
            <!-- User Actions -->
            <div class="relative" x-data="{ open: false }" @click.away="open = false">
                @if (Auth::guard('customer_user')->check())
                    <!-- User is logged in -->
                    <button @click="open = !open"
                        class="flex items-center space-x-2 text-gray-700 hover:text-emerald-600 focus:outline-none">
                        <div class="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center">
                            <span class="text-emerald-700 font-medium">{{ substr(Auth::guard('customer_user')->user()->name, 0, 1) }}</span>
                        </div>
                        <span class="hidden md:inline text-sm font-medium">{{ Auth::guard('customer_user')->user()->name }}</span>
                    </button>
                @else
                    <!-- User is not logged in -->
                    <button @click="open = !open" class="p-1 text-gray-600 hover:text-emerald-600 focus:outline-none hover:bg-gray-100 rounded-full cursor-pointer">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </button>
                @endif
                <!-- Dropdown menu -->
                <div x-show="open" x-transition:enter="transition ease-out duration-100"
                    x-transition:enter-start="transform opacity-0 scale-95"
                    x-transition:enter-end="transform opacity-100 scale-100"
                    x-transition:leave="transition ease-in duration-75"
                    x-transition:leave-start="transform opacity-100 scale-100"
                    x-transition:leave-end="transform opacity-0 scale-95"
                    class="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg py-1 z-50 ring-1 ring-black ring-opacity-5">
                    @if (Auth::guard('customer_user')->check())
                        <div class="py-1">
                            <a href="#"
                                class="flex items-center px-4 py-3 text-base text-gray-700 hover:bg-gray-50 transition-colors duration-150">
                                <i class="fas fa-user-circle w-5 text-center text-emerald-600"></i>
                                <span class="ml-3">{{ __('messages.nav.account') }}</span>
                            </a>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit"
                                    class="w-full text-left flex items-center px-4 py-3 text-base text-red-600 hover:bg-red-50 transition-colors duration-150">
                                    <i class="fas fa-sign-out-alt w-5 text-center"></i>
                                    <span class="ml-3">{{ __('messages.nav.logout') }}</span>
                                </button>
                            </form>
                        </div>
                    @else
                        <div class="py-1">
                            <a href="{{ route('login') }}"
                                class="flex items-center px-4 py-3 text-base text-gray-700 hover:bg-gray-50 transition-colors duration-150">
                                <i class="fas fa-sign-in-alt w-5 text-center text-emerald-600"></i>
                                <span class="ml-3">{{ __('messages.nav.login') }}</span>
                            </a>
                            <a href="{{ route('register') }}"
                                class="flex items-center px-4 py-3 text-base text-gray-700 hover:bg-gray-50 transition-colors duration-150">
                                <i class="fas fa-user-plus w-5 text-center text-emerald-600"></i>
                                <span class="ml-3">{{ __('messages.nav.register') }}</span>
                            </a>
                        </div>
                    @endif
                    <div class="border-t border-gray-100 my-1"></div>
                    <div class="py-1">
                        <a href="#"
                            class="flex items-center px-4 py-3 text-base text-gray-700 hover:bg-gray-50 transition-colors duration-150">
                            <i class="fas fa-question-circle w-5 text-center text-emerald-600"></i>
                            <span class="ml-3">{{ __('messages.nav.help_support') }}</span>
                        </a>
                    </div>
                </div>
            </div>

            <button @click="$store.mobileMenu.toggle()"
                class="p-2 rounded-md text-gray-700 hover:text-emerald-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 cursor-pointer">
                <i x-show="!$store.mobileMenu.open"
                    class="fa-solid fa-bars text-xl w-6 h-6 flex items-center justify-center"></i>
                <i x-show="$store.mobileMenu.open"
                    class="fa-solid fa-times text-xl w-6 h-6 flex items-center justify-center"></i>
                <span class="sr-only">{{ __('messages.ui.close') }}</span>
            </button>
        </div>
        </div>
    </nav>

    <!-- Mobile Menu Panel -->
    <div x-show="$store.mobileMenu.open" x-transition:enter="transition ease-in-out duration-300 transform"
        x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0"
        x-transition:leave="transition ease-in-out duration-300 transform" x-transition:leave-start="translate-x-0"
        x-transition:leave-end="translate-x-full" @keydown.escape.window="$store.mobileMenu.close()"
        class="fixed inset-0 z-50 flex justify-end" style="pointer-events: none;">

        <!-- Menu Content -->
        <div class="relative w-full max-w-sm bg-white shadow-lg h-[100vh] overflow-y-auto flex flex-col md:hidden"
            style="pointer-events: auto; scrollbar-width: none; -ms-overflow-style: none;"
            @click.self="$store.mobileMenu.close()">

            <!-- Mobile Header -->
            <div class="sticky top-0 z-10 bg-white shadow-sm">
                <div
                    class="flex-shrink-0 px-5 py-3 border-b border-gray-100 bg-white flex justify-between items-center h-16">
                    <div class="flex items-center">
                        <img src="{{ asset('images/cslant-logo.png') }}" alt="CSlant Logo" class="h-8"
                            onerror="this.onerror=null;this.src='https://placehold.co/150x40/338146/FFFFFF?text=CSlant';">
                    </div>
                    <button @click="$store.mobileMenu.close()"
                        class="p-1.5 rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 cursor-pointer">
                        <span class="sr-only">{{ __('messages.ui.close') }}</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Mobile Search -->
                <div class="px-5 py-3 border-b border-gray-100 bg-gray-50">
                    <form action="{{ route('templates.index') }}" method="GET" class="relative">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input type="text" name="search" value="{{ request('search') }}"
                                placeholder="{{ __('messages.website.search_placeholder') }}"
                                class="block w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200"
                                autocomplete="off">
                        </div>
                    </form>
                </div>
            </div>

            <!-- Scrollable Menu Content -->
            <div class="flex-1 overflow-y-auto">
                <nav class="space-y-0.5 py-2">
                    <!-- Home Link -->
                    <div class="px-3 py-1">
                        <a href="{{ route('home') }}"
                            class="group flex items-center w-full px-4 py-3 text-base font-medium text-gray-700 hover:bg-emerald-50 rounded-lg hover:text-emerald-600 transition-colors duration-200">
                            <div
                                class="w-9 h-9 rounded-lg bg-emerald-50 flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-emerald-100 transition-colors duration-200">
                                <i class="fas fa-home text-emerald-600 text-base"></i>
                            </div>
                            <span class="leading-tight">{{ __('messages.nav.home') }}</span>
                        </a>
                    </div>

                    @foreach ($menus as $menu)
                        @if ($menu['type'] === 'dropdown' || $menu['type'] === 'mega_menu')
                            <div x-data="{ open: false }" class="space-y-0.5">
                                <div class="px-3 py-1">
                                    <button @click="open = !open"
                                        class="group w-full flex items-center justify-between px-4 py-3 text-base font-medium text-gray-700 hover:bg-emerald-50 rounded-lg hover:text-emerald-600 transition-colors duration-200">
                                        <div class="flex items-center">
                                            <div
                                                class="w-9 h-9 rounded-lg bg-emerald-50 flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-emerald-100 transition-colors duration-200">
                                                <svg class="w-5 h-5 text-emerald-600" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    {!! $menu['svg'] !!}
                                                </svg>
                                            </div>
                                            <span class="leading-tight">{{ $menu['title'] }}</span>
                                        </div>
                                        <svg :class="{ 'rotate-180': open }"
                                            class="h-5 w-5 transform transition-transform duration-200 text-gray-400 group-hover:text-emerald-500"
                                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div x-show="open" x-transition:enter="transition ease-out duration-100"
                                    x-transition:enter-start="opacity-0 transform scale-95"
                                    x-transition:enter-end="opacity-100 transform scale-100"
                                    x-transition:leave="transition ease-in duration-75"
                                    x-transition:leave-start="transform opacity-100 scale-100"
                                    x-transition:leave-end="transform opacity-0 scale-95"
                                    class="pl-5 pr-2 space-y-0.5 ml-11 border-l-2 border-gray-100 py-1">
                                    @if ($menu['type'] === 'dropdown')
                                        @foreach ($menu['items'] as $item)
                                            <a href="{{ getUrl($item) }}"
                                                class="group flex items-center px-4 py-2.5 text-gray-700 hover:bg-emerald-50 rounded-lg transition-colors duration-200">
                                                <div
                                                    class="w-8 h-8 rounded-md bg-emerald-50 flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-emerald-100 transition-colors duration-200">
                                                    <svg class="w-4 h-4 text-emerald-600" fill="none"
                                                        stroke="currentColor" viewBox="0 0 24 24"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        {!! $item['svg'] !!}
                                                    </svg>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <div class="text-sm font-medium text-gray-900 leading-tight">
                                                        {{ $item['title'] }}</div>
                                                    @if (isset($item['subtitle']))
                                                        <div class="text-xs text-gray-500 leading-tight mt-0.5">
                                                            {{ $item['subtitle'] }}</div>
                                                    @endif
                                                </div>
                                            </a>
                                        @endforeach
                                    @elseif($menu['type'] === 'mega_menu')
                                        @foreach ($menu['items'] as $section)
                                            <div class="py-2">
                                                <h4
                                                    class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2 px-4">
                                                    {{ $section['group'] }}</h4>
                                                @foreach ($section['items'] as $item)
                                                    <a href="{{ getUrl($item) }}"
                                                        class="group flex items-center px-4 py-2 text-gray-700 hover:bg-emerald-50 rounded-lg transition-colors duration-200">
                                                        <span
                                                            class="w-1.5 h-1.5 rounded-full bg-emerald-400 mr-2.5 flex-shrink-0"></span>
                                                        <span class="font-medium truncate">{{ $item['title'] }}</span>
                                                    </a>
                                                @endforeach
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                            </div>
                        @else
                            <div class="px-3 py-1">
                                <a href="{{ getUrl($menu) }}"
                                    class="group flex items-center w-full px-4 py-3 text-base font-medium text-gray-700 hover:bg-emerald-50 rounded-lg hover:text-emerald-600 transition-colors duration-200">
                                    <div
                                        class="w-9 h-9 rounded-lg bg-emerald-50 flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-emerald-100 transition-colors duration-200">
                                        <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            {!! $menu['svg'] !!}
                                        </svg>
                                    </div>
                                    <span class="leading-tight">{{ $menu['title'] }}</span>
                                </a>
                            </div>
                        @endif
                    @endforeach
                </nav>
            </div>

            <!-- Auth Buttons -->
            <div class="sticky bottom-0 bg-white border-t border-gray-200 px-5 py-4 mt-auto">
                <a href="{{ route('login') }}"
                    class="block w-full px-4 py-3 text-center text-sm font-medium text-emerald-600 hover:bg-emerald-50 rounded-lg border-2 border-emerald-600 mb-3 transition-all duration-200 hover:shadow-sm hover:-translate-y-0.5">
                    <i class="fas fa-sign-in-alt mr-2"></i> {{ __('auth.login') }}
                </a>
                <a href="{{ route('register') }}"
                    class="block w-full px-4 py-3 text-center text-sm font-medium text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md hover:-translate-y-0.5">
                    <i class="fas fa-user-plus mr-2"></i> {{ __('auth.register') }}
                </a>
            </div>
        </div> <!-- Close menu content div -->
    </div> <!-- Close mobile menu panel -->
</header>
