@props([
    'name' => '',
    'label' => '',
    'placeholder' => '',
    'value' => '',
    'required' => false,
    'disabled' => false,
    'readonly' => false,
    'rows' => 4,
    'class' => '',
    'containerClass' => '',
    'labelClass' => '',
    'helpText' => '',
    'maxlength' => null,
    'showCounter' => false
])

<div class="form-group {{ $containerClass }}">
    @if($label)
        <label for="{{ $name }}" class="block text-sm font-medium text-gray-700 mb-2 {{ $labelClass }}">
            {{ $label }}
            @if($required)
                <span class="text-red-500 ml-1">*</span>
            @endif
        </label>
    @endif
    
    <div class="relative">
        <textarea 
            name="{{ $name }}"
            id="{{ $name }}"
            rows="{{ $rows }}"
            placeholder="{{ $placeholder }}"
            @if($required) required @endif
            @if($disabled) disabled @endif
            @if($readonly) readonly @endif
            @if($maxlength) maxlength="{{ $maxlength }}" @endif
            class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 transition-colors duration-200 resize-vertical {{ $class }}"
            {{ $attributes }}
        >{{ old($name, $value) }}</textarea>
        
        @if($showCounter && $maxlength)
            <div class="absolute bottom-2 right-2 text-xs text-gray-400 bg-white px-1">
                <span class="char-count">{{ strlen(old($name, $value)) }}</span>/{{ $maxlength }}
            </div>
        @endif
    </div>
    
    @if($helpText)
        <p class="mt-1 text-sm text-gray-500">{{ $helpText }}</p>
    @endif
    
    @error($name)
        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
    @enderror
</div>

@if($showCounter && $maxlength)
<script>
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('{{ $name }}');
    const counter = textarea.parentNode.querySelector('.char-count');
    
    if (textarea && counter) {
        textarea.addEventListener('input', function() {
            counter.textContent = this.value.length;
            
            // Change color when approaching limit
            const remaining = {{ $maxlength }} - this.value.length;
            if (remaining < 20) {
                counter.parentNode.classList.add('text-red-500');
                counter.parentNode.classList.remove('text-gray-400');
            } else {
                counter.parentNode.classList.add('text-gray-400');
                counter.parentNode.classList.remove('text-red-500');
            }
        });
    }
});
</script>
@endif
