@props([
    'name' => '',
    'label' => '',
    'options' => [],
    'value' => '',
    'required' => false,
    'disabled' => false,
    'multiple' => false,
    'class' => '',
    'containerClass' => '',
    'labelClass' => '',
    'helpText' => '',
    'placeholder' => 'Select an option...',
    'searchable' => false
])

<div class="form-group {{ $containerClass }}">
    @if($label)
        <label for="{{ $name }}" class="block text-sm font-medium text-gray-700 mb-2 {{ $labelClass }}">
            {{ $label }}
            @if($required)
                <span class="text-red-500 ml-1">*</span>
            @endif
        </label>
    @endif
    
    @if($searchable)
        <!-- Searchable Select -->
        <div class="relative" x-data="searchableSelect({
            options: {{ json_encode($options) }},
            value: '{{ old($name, $value) }}',
            multiple: {{ $multiple ? 'true' : 'false' }},
            placeholder: '{{ $placeholder }}'
        })">
            <div class="relative">
                <input
                    type="text"
                    x-model="search"
                    @click="open = true"
                    @keydown.escape="open = false"
                    @keydown.arrow-down.prevent="highlightNext()"
                    @keydown.arrow-up.prevent="highlightPrevious()"
                    @keydown.enter.prevent="selectHighlighted()"
                    class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 transition-colors duration-200 {{ $class }}"
                    :placeholder="selectedText || '{{ $placeholder }}'"
                    autocomplete="off"
                >
                <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                    </svg>
                </div>
            </div>
            
            <!-- Hidden input for form submission -->
            <input type="hidden" name="{{ $name }}" :value="selectedValue">
            
            <!-- Dropdown -->
            <div x-show="open" 
                 x-transition:enter="transition ease-out duration-100"
                 x-transition:enter-start="transform opacity-0 scale-95"
                 x-transition:enter-end="transform opacity-100 scale-100"
                 x-transition:leave="transition ease-in duration-75"
                 x-transition:leave-start="transform opacity-100 scale-100"
                 x-transition:leave-end="transform opacity-0 scale-95"
                 @click.away="open = false"
                 class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
                
                <template x-for="(option, index) in filteredOptions" :key="option.value">
                    <div @click="selectOption(option)"
                         :class="{ 'bg-emerald-100 text-emerald-900': index === highlightedIndex, 'text-gray-900': index !== highlightedIndex }"
                         class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-emerald-50">
                        <span x-text="option.label" :class="{ 'font-medium': isSelected(option.value), 'font-normal': !isSelected(option.value) }" class="block truncate"></span>
                        <span x-show="isSelected(option.value)" class="absolute inset-y-0 right-0 flex items-center pr-4 text-emerald-600">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                        </span>
                    </div>
                </template>
                
                <div x-show="filteredOptions.length === 0" class="py-2 pl-3 pr-9 text-gray-500">
                    No options found
                </div>
            </div>
        </div>
    @else
        <!-- Regular Select -->
        <select 
            name="{{ $name }}{{ $multiple ? '[]' : '' }}"
            id="{{ $name }}"
            @if($required) required @endif
            @if($disabled) disabled @endif
            @if($multiple) multiple @endif
            class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 transition-colors duration-200 {{ $class }}"
            {{ $attributes }}
        >
            @if(!$multiple && $placeholder)
                <option value="">{{ $placeholder }}</option>
            @endif
            
            @foreach($options as $optionValue => $optionLabel)
                @if(is_array($optionLabel))
                    <optgroup label="{{ $optionValue }}">
                        @foreach($optionLabel as $subValue => $subLabel)
                            <option value="{{ $subValue }}" 
                                @if(old($name, $value) == $subValue || (is_array(old($name, $value)) && in_array($subValue, old($name, $value)))) selected @endif>
                                {{ $subLabel }}
                            </option>
                        @endforeach
                    </optgroup>
                @else
                    <option value="{{ $optionValue }}" 
                        @if(old($name, $value) == $optionValue || (is_array(old($name, $value)) && in_array($optionValue, old($name, $value)))) selected @endif>
                        {{ $optionLabel }}
                    </option>
                @endif
            @endforeach
        </select>
    @endif
    
    @if($helpText)
        <p class="mt-1 text-sm text-gray-500">{{ $helpText }}</p>
    @endif
    
    @error($name)
        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
    @enderror
</div>

@if($searchable)
<script>
function searchableSelect(config) {
    return {
        options: config.options,
        filteredOptions: config.options,
        selectedValue: config.multiple ? [] : config.value,
        selectedText: '',
        search: '',
        open: false,
        highlightedIndex: -1,
        multiple: config.multiple,
        
        init() {
            this.updateSelectedText();
            this.$watch('search', () => this.filterOptions());
        },
        
        filterOptions() {
            if (!this.search) {
                this.filteredOptions = this.options;
            } else {
                this.filteredOptions = this.options.filter(option => 
                    option.label.toLowerCase().includes(this.search.toLowerCase())
                );
            }
            this.highlightedIndex = -1;
        },
        
        selectOption(option) {
            if (this.multiple) {
                const index = this.selectedValue.indexOf(option.value);
                if (index > -1) {
                    this.selectedValue.splice(index, 1);
                } else {
                    this.selectedValue.push(option.value);
                }
            } else {
                this.selectedValue = option.value;
                this.open = false;
            }
            this.updateSelectedText();
            this.search = '';
        },
        
        isSelected(value) {
            return this.multiple ? this.selectedValue.includes(value) : this.selectedValue === value;
        },
        
        updateSelectedText() {
            if (this.multiple) {
                const selected = this.options.filter(option => this.selectedValue.includes(option.value));
                this.selectedText = selected.length > 0 ? `${selected.length} selected` : '';
            } else {
                const selected = this.options.find(option => option.value === this.selectedValue);
                this.selectedText = selected ? selected.label : '';
            }
        },
        
        highlightNext() {
            this.highlightedIndex = Math.min(this.highlightedIndex + 1, this.filteredOptions.length - 1);
        },
        
        highlightPrevious() {
            this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
        },
        
        selectHighlighted() {
            if (this.highlightedIndex >= 0 && this.filteredOptions[this.highlightedIndex]) {
                this.selectOption(this.filteredOptions[this.highlightedIndex]);
            }
        }
    }
}
</script>
@endif
