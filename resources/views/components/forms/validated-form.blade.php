@props([
    'action' => '#',
    'method' => 'POST',
    'id' => 'validated-form',
    'class' => '',
    'validation' => [],
    'realtime' => true,
    'showSuccess' => true
])

<form 
    id="{{ $id }}"
    action="{{ $action }}" 
    method="{{ $method }}"
    class="validated-form {{ $class }}"
    data-validation="{{ json_encode($validation) }}"
    data-realtime="{{ $realtime ? 'true' : 'false' }}"
    data-show-success="{{ $showSuccess ? 'true' : 'false' }}"
    novalidate>
    
    @if($method !== 'GET')
        @csrf
    @endif
    
    @if(in_array(strtoupper($method), ['PUT', 'PATCH', 'DELETE']))
        @method($method)
    @endif
    
    {{ $slot }}
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('{{ $id }}');
    if (!form) return;
    
    // Import FormValidator dynamically
    import('resources/js/utils/form-validator.js').then(({ FormValidator }) => {
        const validation = JSON.parse(form.dataset.validation || '{}');
        const realtime = form.dataset.realtime === 'true';
        const showSuccess = form.dataset.showSuccess === 'true';
        
        const validator = new FormValidator(form, {
            validateOnInput: realtime,
            validateOnBlur: realtime,
            showErrors: true,
            errorClass: 'border-red-500 focus:border-red-500 focus:ring-red-500',
            successClass: showSuccess ? 'border-green-500 focus:border-green-500 focus:ring-green-500' : '',
            errorMessageClass: 'form-error-message'
        });
        
        // Add validation rules
        validator.addRules(validation);
        
        // Store validator instance for external access
        form.validator = validator;
    }).catch(error => {
        console.error('Failed to load FormValidator:', error);
    });
});
</script>

<style>
.form-error-message {
    @apply text-red-500 text-sm mt-1 block;
}

.validated-form .border-red-500 {
    @apply border-red-500;
}

.validated-form .border-green-500 {
    @apply border-green-500;
}

.validated-form .focus\:border-red-500:focus {
    @apply border-red-500 ring-red-500;
}

.validated-form .focus\:border-green-500:focus {
    @apply border-green-500 ring-green-500;
}

.validated-form .focus\:ring-red-500:focus {
    @apply ring-red-500;
}

.validated-form .focus\:ring-green-500:focus {
    @apply ring-green-500;
}
</style>
