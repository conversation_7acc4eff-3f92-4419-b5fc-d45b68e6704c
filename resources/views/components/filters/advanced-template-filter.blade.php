@props([
    'categories' => collect(),
    'allFeatures' => collect(),
    'priceRange' => null,
    'currentFilters' => []
])

<div x-data="advancedTemplateFilter({{ json_encode($currentFilters) }})" class="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-bold text-emerald-700">{{ __('messages.website.filters') }}</h2>
        <button @click="clearAllFilters()" 
                x-show="hasActiveFilters()"
                class="text-sm text-gray-500 hover:text-emerald-600 underline">
            {{ __('messages.website.clear_all') }}
        </button>
    </div>

    <form @submit.prevent="applyFilters()" class="space-y-6">
        <!-- Search -->
        <div>
            <label class="block text-sm font-semibold text-gray-700 mb-2">
                {{ __('messages.website.search') }}
            </label>
            <div class="relative">
                <input type="text" 
                       x-model="filters.search"
                       placeholder="{{ __('messages.website.search_placeholder') }}"
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <i class="fa-solid fa-search text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div>
            <label class="block text-sm font-semibold text-gray-700 mb-3">
                {{ __('messages.website.categories') }}
            </label>
            <div class="space-y-2 max-h-40 overflow-y-auto">
                @foreach($categories as $category)
                <label class="flex items-center">
                    <input type="radio" 
                           name="category" 
                           value="{{ $category->slug }}"
                           x-model="filters.category"
                           class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                    <span class="ml-2 text-sm text-gray-700">
                        {{ $category->name }} 
                        <span class="text-gray-500">({{ $category->templates_count }})</span>
                    </span>
                </label>
                @endforeach
                <label class="flex items-center">
                    <input type="radio" 
                           name="category" 
                           value=""
                           x-model="filters.category"
                           class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                    <span class="ml-2 text-sm text-gray-700">{{ __('messages.website.all_categories') }}</span>
                </label>
            </div>
        </div>

        <!-- Features -->
        <div>
            <label class="block text-sm font-semibold text-gray-700 mb-3">
                {{ __('messages.website.features') }}
            </label>
            <div class="space-y-2 max-h-40 overflow-y-auto">
                @foreach($allFeatures as $feature)
                <label class="flex items-center">
                    <input type="checkbox" 
                           value="{{ $feature }}"
                           x-model="filters.features"
                           class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                    <span class="ml-2 text-sm text-gray-700">{{ $feature }}</span>
                </label>
                @endforeach
            </div>
        </div>

        <!-- Price Range -->
        @if($priceRange)
        <div>
            <label class="block text-sm font-semibold text-gray-700 mb-3">
                {{ __('messages.website.price_range') }}
            </label>
            <div class="space-y-3">
                <div class="flex items-center space-x-2">
                    <input type="number" 
                           x-model="filters.price_min"
                           placeholder="{{ number_format($priceRange->min_price, 0, ',', '.') }}"
                           min="{{ $priceRange->min_price }}"
                           max="{{ $priceRange->max_price }}"
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    <span class="text-gray-500">-</span>
                    <input type="number" 
                           x-model="filters.price_max"
                           placeholder="{{ number_format($priceRange->max_price, 0, ',', '.') }}"
                           min="{{ $priceRange->min_price }}"
                           max="{{ $priceRange->max_price }}"
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                </div>
                <div class="text-xs text-gray-500">
                    {{ __('messages.website.price_range_hint', [
                        'min' => number_format($priceRange->min_price, 0, ',', '.'),
                        'max' => number_format($priceRange->max_price, 0, ',', '.')
                    ]) }}
                </div>
            </div>
        </div>
        @endif

        <!-- Special Filters -->
        <div>
            <label class="block text-sm font-semibold text-gray-700 mb-3">
                {{ __('messages.website.special_filters') }}
            </label>
            <div class="space-y-2">
                <label class="flex items-center">
                    <input type="checkbox" 
                           x-model="filters.is_featured"
                           class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                    <span class="ml-2 text-sm text-gray-700">{{ __('messages.website.featured_only') }}</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" 
                           x-model="filters.has_sale"
                           class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                    <span class="ml-2 text-sm text-gray-700">{{ __('messages.website.on_sale_only') }}</span>
                </label>
            </div>
        </div>

        <!-- Sort Options -->
        <div>
            <label class="block text-sm font-semibold text-gray-700 mb-3">
                {{ __('messages.website.sort_by') }}
            </label>
            <select x-model="filters.sort" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                <option value="newest">{{ __('messages.website.newest') }}</option>
                <option value="popular">{{ __('messages.website.popular') }}</option>
                <option value="featured">{{ __('messages.website.featured') }}</option>
                <option value="price_low">{{ __('messages.website.price_low_to_high') }}</option>
                <option value="price_high">{{ __('messages.website.price_high_to_low') }}</option>
                <option value="name">{{ __('messages.website.name_a_to_z') }}</option>
            </select>
        </div>

        <!-- Apply Filters Button -->
        <div class="pt-4 border-t border-gray-200">
            <button type="submit" 
                    class="w-full bg-emerald-600 text-white py-2 px-4 rounded-lg hover:bg-emerald-700 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-colors">
                {{ __('messages.website.apply_filters') }}
            </button>
        </div>
    </form>

    <!-- Active Filters Display -->
    <div x-show="hasActiveFilters()" class="mt-6 pt-4 border-t border-gray-200">
        <h3 class="text-sm font-semibold text-gray-700 mb-2">{{ __('messages.website.active_filters') }}</h3>
        <div class="flex flex-wrap gap-2">
            <!-- Search Filter -->
            <template x-if="filters.search">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-emerald-100 text-emerald-800">
                    <span x-text="'{{ __('messages.website.search') }}: ' + filters.search"></span>
                    <button @click="filters.search = ''" class="ml-1 text-emerald-600 hover:text-emerald-800">
                        <i class="fa-solid fa-times"></i>
                    </button>
                </span>
            </template>
            
            <!-- Category Filter -->
            <template x-if="filters.category">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                    <span x-text="'{{ __('messages.website.category') }}: ' + getCategoryName(filters.category)"></span>
                    <button @click="filters.category = ''" class="ml-1 text-blue-600 hover:text-blue-800">
                        <i class="fa-solid fa-times"></i>
                    </button>
                </span>
            </template>
            
            <!-- Features Filter -->
            <template x-for="feature in filters.features" :key="feature">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                    <span x-text="feature"></span>
                    <button @click="removeFeature(feature)" class="ml-1 text-purple-600 hover:text-purple-800">
                        <i class="fa-solid fa-times"></i>
                    </button>
                </span>
            </template>
            
            <!-- Price Range Filter -->
            <template x-if="filters.price_min || filters.price_max">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                    <span x-text="'{{ __('messages.website.price') }}: ' + formatPriceRange()"></span>
                    <button @click="filters.price_min = ''; filters.price_max = ''" class="ml-1 text-yellow-600 hover:text-yellow-800">
                        <i class="fa-solid fa-times"></i>
                    </button>
                </span>
            </template>
        </div>
    </div>
</div>

@push('scripts')
<script>
function advancedTemplateFilter(initialFilters = {}) {
    return {
        filters: {
            search: initialFilters.search || '',
            category: initialFilters.category || '',
            features: Array.isArray(initialFilters.features) ? initialFilters.features : [],
            price_min: initialFilters.price_min || '',
            price_max: initialFilters.price_max || '',
            is_featured: initialFilters.is_featured || false,
            has_sale: initialFilters.has_sale || false,
            sort: initialFilters.sort || 'newest'
        },
        
        categories: @json($categories->pluck('name', 'slug')),
        
        applyFilters() {
            const params = new URLSearchParams();
            
            // Add non-empty filters to URL
            Object.keys(this.filters).forEach(key => {
                const value = this.filters[key];
                if (value !== '' && value !== false && !(Array.isArray(value) && value.length === 0)) {
                    if (Array.isArray(value)) {
                        value.forEach(v => params.append(key + '[]', v));
                    } else {
                        params.append(key, value);
                    }
                }
            });
            
            // Navigate to filtered results
            window.location.href = '{{ route('templates.index') }}?' + params.toString();
        },
        
        clearAllFilters() {
            this.filters = {
                search: '',
                category: '',
                features: [],
                price_min: '',
                price_max: '',
                is_featured: false,
                has_sale: false,
                sort: 'newest'
            };
            this.applyFilters();
        },
        
        hasActiveFilters() {
            return this.filters.search !== '' ||
                   this.filters.category !== '' ||
                   this.filters.features.length > 0 ||
                   this.filters.price_min !== '' ||
                   this.filters.price_max !== '' ||
                   this.filters.is_featured ||
                   this.filters.has_sale ||
                   this.filters.sort !== 'newest';
        },
        
        getCategoryName(slug) {
            return this.categories[slug] || slug;
        },
        
        removeFeature(feature) {
            this.filters.features = this.filters.features.filter(f => f !== feature);
        },
        
        formatPriceRange() {
            const min = this.filters.price_min ? new Intl.NumberFormat('vi-VN').format(this.filters.price_min) + 'đ' : '';
            const max = this.filters.price_max ? new Intl.NumberFormat('vi-VN').format(this.filters.price_max) + 'đ' : '';
            
            if (min && max) return min + ' - ' + max;
            if (min) return 'Từ ' + min;
            if (max) return 'Đến ' + max;
            return '';
        }
    }
}
</script>
@endpush
