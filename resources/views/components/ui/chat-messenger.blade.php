@php
    $messengerUrl = app(\App\Settings\SiteSetting::class)->messenger_url ?? 'https://messenger.com/t/cslant.official';
@endphp

<div x-data="chatMessenger()" class="fixed bottom-4 right-4 z-50">
    <!-- Chat <PERSON> -->
    <button @click="toggleChat()" 
            :class="isOpen ? 'bg-red-500 hover:bg-red-600' : 'bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 hover:from-blue-600 hover:via-blue-700 hover:to-purple-700'"
            class="group relative w-14 h-14 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        
        <!-- Chat Icon -->
        <svg x-show="!isOpen" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-2.697-.413l-3.178 1.589a1 1 0 01-1.414-1.414l1.589-3.178A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
        </svg>
        
        <!-- Close Icon -->
        <svg x-show="isOpen" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>

        <!-- Notification Badge -->
        <span x-show="hasNewMessage" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <span class="w-2 h-2 bg-white rounded-full animate-pulse"></span>
        </span>
    </button>

    <!-- Chat Popup -->
    <div x-show="isOpen" 
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95 translate-y-4"
         x-transition:enter-end="opacity-100 scale-100 translate-y-0"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100 translate-y-0"
         x-transition:leave-end="opacity-0 scale-95 translate-y-4"
         class="absolute bottom-16 right-0 w-80 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 overflow-hidden"
         style="display: none;">
        
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-2.697-.413l-3.178 1.589a1 1 0 01-1.414-1.414l1.589-3.178A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold">{{ __('messages.ui.chat_support') }}</h3>
                        <p class="text-xs opacity-90">{{ __('messages.ui.online_now') }}</p>
                    </div>
                </div>
                <button @click="toggleChat()" class="text-white/80 hover:text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Messages Area -->
        <div class="flex-1 p-4 h-64 overflow-y-auto bg-gray-50">
            <!-- Welcome Message -->
            <div class="mb-4">
                <div class="flex items-start space-x-2">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <div class="bg-white rounded-lg p-3 shadow-sm max-w-xs">
                        <p class="text-sm text-gray-800">{{ __('messages.ui.chat_welcome') }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ now()->format('H:i') }}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="space-y-2">
                <button @click="openMessenger()" class="w-full text-left bg-white hover:bg-blue-50 rounded-lg p-3 shadow-sm border border-gray-200 transition">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-2.697-.413l-3.178 1.589a1 1 0 01-1.414-1.414l1.589-3.178A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ __('messages.ui.chat_messenger') }}</p>
                            <p class="text-xs text-gray-500">{{ __('messages.ui.chat_messenger_desc') }}</p>
                        </div>
                    </div>
                </button>

                <button @click="$store.modal.open()" class="w-full text-left bg-white hover:bg-emerald-50 rounded-lg p-3 shadow-sm border border-gray-200 transition">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ __('messages.ui.contact_form') }}</p>
                            <p class="text-xs text-gray-500">{{ __('messages.ui.contact_form_desc') }}</p>
                        </div>
                    </div>
                </button>
            </div>
        </div>

        <!-- Footer -->
        <div class="p-3 bg-white border-t border-gray-200">
            <p class="text-xs text-gray-500 text-center">{{ __('messages.ui.chat_footer') }}</p>
        </div>
    </div>
</div>

<script>
    function chatMessenger() {
        return {
            isOpen: false,
            hasNewMessage: false,
            messengerUrl: '{{ $messengerUrl }}',

            init() {
                // Show notification badge after 10 seconds if chat hasn't been opened
                setTimeout(() => {
                    if (!this.isOpen) {
                        this.hasNewMessage = true;
                    }
                }, 10000);
            },

            toggleChat() {
                this.isOpen = !this.isOpen;
                if (this.isOpen) {
                    this.hasNewMessage = false;
                }
            },

            openMessenger() {
                window.open(this.messengerUrl, '_blank', 'width=800,height=600');
                this.toggleChat();
            }
        }
    }
</script>
