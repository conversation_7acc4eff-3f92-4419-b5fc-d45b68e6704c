<!-- Chat Widget Component -->
<div id="chatWidget" class="fixed bottom-6 right-6 z-50" x-data="chatWidget()">
    <!-- Chat Button -->
    <div x-show="!isOpen" 
         @click="toggleChat()"
         class="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white rounded-full p-4 shadow-lg cursor-pointer transition-all duration-300 hover:scale-110 group">
        <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
        </svg>
        
        <!-- Notification Badge -->
        <div class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center animate-pulse">
            <span class="text-xs font-bold">!</span>
        </div>
    </div>

    <!-- Chat Panel -->
    <div x-show="isOpen" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95 translate-y-4"
         x-transition:enter-end="opacity-100 transform scale-100 translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100 translate-y-0"
         x-transition:leave-end="opacity-0 transform scale-95 translate-y-4"
         class="bg-white rounded-2xl shadow-2xl w-80 h-96 flex flex-col overflow-hidden border border-gray-200">
        
        <!-- Header -->
        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white p-4 flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <div>
                    <h3 class="font-semibold text-sm">{{ __('messages.chat.support_title') }}</h3>
                    <p class="text-xs text-emerald-100">{{ __('messages.chat.online_status') }}</p>
                </div>
            </div>
            <button @click="toggleChat()" class="text-white/80 hover:text-white transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <!-- Chat Options -->
        <div class="flex-1 p-4 space-y-3">
            <div class="text-sm text-gray-600 mb-4">
                {{ __('messages.chat.choose_platform') }}
            </div>

            <!-- Facebook Messenger -->
            <button @click="openMessenger()" 
                    class="w-full flex items-center space-x-3 p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200 group">
                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0C5.374 0 0 4.975 0 11.111c0 3.497 1.745 6.616 4.472 8.652V24l4.086-2.242c1.09.301 2.246.464 3.442.464 6.626 0 12-4.974 12-11.111C24 4.975 18.626 0 12 0zm1.191 14.963l-3.055-3.26-5.963 3.26L10.732 8.1l3.13 3.26L19.77 8.1l-6.579 6.863z"/>
                    </svg>
                </div>
                <div class="flex-1 text-left">
                    <div class="font-medium text-gray-900 group-hover:text-blue-700">{{ __('messages.chat.messenger_title') }}</div>
                    <div class="text-xs text-gray-500">{{ __('messages.chat.messenger_description') }}</div>
                </div>
                <svg class="w-4 h-4 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
            </button>

            <!-- Zalo -->
            <button @click="openZalo()" 
                    class="w-full flex items-center space-x-3 p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200 group">
                <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.568 8.16c-.169-.224-.487-.336-.95-.336-.462 0-.781.112-.95.336-.169.224-.253.499-.253.825 0 .326.084.601.253.825.169.224.488.336.95.336.463 0 .781-.112.95-.336.169-.224.253-.499.253-.825 0-.326-.084-.601-.253-.825zm-11.136 0c-.169-.224-.487-.336-.95-.336-.462 0-.781.112-.95.336-.169.224-.253.499-.253.825 0 .326.084.601.253.825.169.224.488.336.95.336.463 0 .781-.112.95-.336.169-.224.253-.499.253-.825 0-.326-.084-.601-.253-.825zM12 15.492c-1.948 0-3.532-1.584-3.532-3.532S10.052 8.428 12 8.428s3.532 1.584 3.532 3.532S13.948 15.492 12 15.492z"/>
                    </svg>
                </div>
                <div class="flex-1 text-left">
                    <div class="font-medium text-gray-900 group-hover:text-blue-700">{{ __('messages.chat.zalo_title') }}</div>
                    <div class="text-xs text-gray-500">{{ __('messages.chat.zalo_description') }}</div>
                </div>
                <svg class="w-4 h-4 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
            </button>

            <!-- Contact Form -->
            <button @click="openContactForm()" 
                    class="w-full flex items-center space-x-3 p-3 bg-emerald-50 hover:bg-emerald-100 rounded-lg transition-colors duration-200 group">
                <div class="w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                </div>
                <div class="flex-1 text-left">
                    <div class="font-medium text-gray-900 group-hover:text-emerald-700">{{ __('messages.chat.form_title') }}</div>
                    <div class="text-xs text-gray-500">{{ __('messages.chat.form_description') }}</div>
                </div>
                <svg class="w-4 h-4 text-gray-400 group-hover:text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
            </button>
        </div>

        <!-- Footer -->
        <div class="p-3 bg-gray-50 text-center">
            <p class="text-xs text-gray-500">{{ __('messages.chat.response_time') }}</p>
        </div>
    </div>
</div>

<!-- Chat widget uses direct links instead of embedded SDKs for better performance -->

<script>
function chatWidget() {
    return {
        isOpen: false,
        
        toggleChat() {
            this.isOpen = !this.isOpen;
        },
        
        openMessenger() {
            // Open Facebook Messenger in new window
            const messengerUrl = '{{ config("services.facebook.messenger_url", "https://m.me/cslant.official") }}';
            window.open(messengerUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
            this.isOpen = false;
        },

        openZalo() {
            // Open Zalo chat in new window
            const zaloUrl = '{{ config("services.zalo.chat_url", "https://zalo.me/cslant.official") }}';
            window.open(zaloUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
            this.isOpen = false;
        },

        openContactForm() {
            // Open contact form modal
            if (typeof Alpine !== 'undefined' && Alpine.store('contactModal')) {
                Alpine.store('contactModal').open();
            } else if (typeof Alpine !== 'undefined' && Alpine.store('modal')) {
                Alpine.store('modal').open();
            } else {
                // Fallback: scroll to contact section or open contact page
                const contactSection = document.getElementById('contact');
                if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' });
                } else {
                    window.location.href = '/lien-he';
                }
            }
            this.isOpen = false;
        },
        
        // Removed Facebook Messenger SDK initialization - using direct links instead
    }
}

// Chat widget is ready to use with direct links
</script>
