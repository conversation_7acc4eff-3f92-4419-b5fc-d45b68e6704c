@component('mail::message')
# {{ __('messages.emails.password_reset.title') }}

{{ __('messages.emails.password_reset.greeting') }},

{{ __('messages.emails.password_reset.message') }}

@component('mail::button', ['url' => $resetUrl])
{{ __('messages.emails.password_reset.action') }}
@endcomponent

{{ __('messages.emails.password_reset.expiry_notice', ['minutes' => config('auth.passwords.'.config('auth.defaults.passwords').'.expire')]) }}

{{ __('messages.emails.password_reset.ignore_notice') }}

{{ __('messages.emails.password_reset.regards') }},<br>
{{ config('app.name') }}
@endcomponent
