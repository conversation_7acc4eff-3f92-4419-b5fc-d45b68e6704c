import { HomePage } from './pages/home';
import Alpine from 'alpinejs';

export class CSlantApp {
    constructor() {
        this.homePage = new HomePage();
    }

    init() {
        // Initialize Alpine stores
        document.addEventListener('alpine:init', () => {
            Alpine.store('mobileMenu', {
                open: false,
                toggle() {
                    this.open = !this.open;
                },
                close() {
                    this.open = false;
                }
            });

            Alpine.store('searchModal', {
                open: false,
                toggle() {
                    this.open = !this.open;
                },
                close() {
                    this.open = false;
                }
            });
        });

        this.homePage.init();
    }
}
