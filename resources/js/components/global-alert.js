/**
 * Global Alert System
 * Provides a centralized way to show notifications across the application
 */

class GlobalAlertSystem {
    constructor() {
        this.defaultOptions = {
            title: null,
            duration: 5000,
            autoHide: true,
            dismissible: true,
            showProgress: true,
            position: 'top-right'
        };
    }

    /**
     * Show an alert
     * @param {string} message - The message to display
     * @param {string} type - Alert type: success, error, warning, info
     * @param {object} options - Additional options
     * @returns {string} Alert ID
     */
    show(message, type = 'info', options = {}) {
        const config = { ...this.defaultOptions, ...options };
        
        // Create alert element
        const alertId = 'global-alert-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        const alertHtml = this.createAlertHtml(alertId, message, type, config);
        
        // Insert into DOM
        const container = this.getOrCreateContainer(config.position);
        container.insertAdjacentHTML('beforeend', alertHtml);
        
        // Initialize Alpine component if available
        const alertElement = document.getElementById(alertId);
        if (alertElement && typeof Alpine !== 'undefined') {
            Alpine.initTree(alertElement);
        }
        
        return alertId;
    }

    /**
     * Create HTML for alert
     */
    createAlertHtml(id, message, type, config) {
        const styles = this.getAlertStyles(type);
        const icon = this.getIcon(type);
        
        return `
            <div id="${id}" 
                 x-data="globalAlertComponent(${config.autoHide}, ${config.duration})" 
                 x-show="show" 
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform translate-x-full"
                 x-transition:enter-end="opacity-100 transform translate-x-0"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform translate-x-0"
                 x-transition:leave-end="opacity-0 transform translate-x-full"
                 class="relative mb-3 p-4 border rounded-lg ${styles.container} shadow-lg max-w-sm pointer-events-auto"
                 role="alert"
                 @mouseenter="pause()"
                 @mouseleave="resume()">
                 
                ${config.showProgress && config.autoHide ? `
                    <div class="absolute top-0 left-0 h-1 ${styles.progress} rounded-t-lg transition-all duration-100 ease-linear"
                         :style="\`width: \${progress}%\`"></div>
                ` : ''}
                
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 ${styles.icon}" fill="currentColor" viewBox="0 0 20 20">
                            ${icon}
                        </svg>
                    </div>
                    
                    <div class="flex-1 min-w-0 ml-3">
                        ${config.title ? `<h3 class="text-sm font-semibold mb-1">${config.title}</h3>` : ''}
                        <div class="text-sm">${message}</div>
                    </div>
                    
                    ${config.dismissible ? `
                        <div class="flex-shrink-0 ml-3">
                            <button @click="hide()" 
                                    class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 hover:bg-emerald-200 hover:bg-opacity-10 transition-colors ${styles.focusRing}">
                                <span class="sr-only">Đóng</span>
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </button>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Get alert styles based on type
     */
    getAlertStyles(type) {
        const styles = {
            'success': {
                container: 'bg-green-50 border-green-200 text-green-800',
                icon: 'text-green-500',
                progress: 'bg-green-500',
                focusRing: 'focus:ring-green-600'
            },
            'error': {
                container: 'bg-red-50 border-red-200 text-red-800',
                icon: 'text-red-500',
                progress: 'bg-red-500',
                focusRing: 'focus:ring-red-600'
            },
            'warning': {
                container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
                icon: 'text-yellow-500',
                progress: 'bg-yellow-500',
                focusRing: 'focus:ring-yellow-600'
            },
            'info': {
                container: 'bg-blue-50 border-blue-200 text-blue-800',
                icon: 'text-blue-500',
                progress: 'bg-blue-500',
                focusRing: 'focus:ring-blue-600'
            }
        };
        
        return styles[type] || styles.info;
    }

    /**
     * Get icon SVG path based on type
     */
    getIcon(type) {
        const icons = {
            'success': '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>',
            'error': '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>',
            'warning': '<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>',
            'info': '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>'
        };
        
        return icons[type] || icons.info;
    }

    /**
     * Get or create container for alerts
     */
    getOrCreateContainer(position) {
        const containerId = 'global-alerts-' + position;
        let container = document.getElementById(containerId);
        
        if (!container) {
            container = document.createElement('div');
            container.id = containerId;
            container.className = this.getContainerClasses(position);
            document.body.appendChild(container);
        }
        
        return container;
    }

    /**
     * Get container CSS classes based on position
     */
    getContainerClasses(position) {
        const baseClasses = 'fixed z-50 pointer-events-none';
        const positions = {
            'top-right': 'top-4 right-4',
            'top-left': 'top-4 left-4',
            'bottom-right': 'bottom-4 right-4',
            'bottom-left': 'bottom-4 left-4',
            'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
            'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
        };
        
        return `${baseClasses} ${positions[position] || positions['top-right']}`;
    }

    /**
     * Remove alert by ID
     */
    remove(alertId) {
        const element = document.getElementById(alertId);
        if (element) {
            element.remove();
        }
    }

    /**
     * Clear all alerts
     */
    clearAll() {
        const containers = document.querySelectorAll('[id^="global-alerts-"]');
        containers.forEach(container => {
            container.innerHTML = '';
        });
    }

    // Convenience methods
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    error(message, options = {}) {
        return this.show(message, 'error', { ...options, autoHide: false });
    }

    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    info(message, options = {}) {
        return this.show(message, 'info', options);
    }
}

// Alpine.js component for individual alerts
window.globalAlertComponent = function(autoHide = true, duration = 5000) {
    return {
        show: true,
        progress: 100,
        interval: null,
        isPaused: false,
        
        init() {
            if (autoHide) {
                this.startCountdown();
            }
        },
        
        startCountdown() {
            if (this.isPaused) return;
            
            const step = 100 / (duration / 100);
            this.interval = setInterval(() => {
                if (!this.isPaused) {
                    this.progress -= step;
                    if (this.progress <= 0) {
                        this.hide();
                    }
                }
            }, 100);
        },
        
        hide() {
            if (this.interval) {
                clearInterval(this.interval);
            }
            this.show = false;
            
            // Remove element after transition
            setTimeout(() => {
                if (this.$el && this.$el.parentNode) {
                    this.$el.parentNode.removeChild(this.$el);
                }
            }, 300);
        },
        
        pause() {
            this.isPaused = true;
            if (this.interval) {
                clearInterval(this.interval);
            }
        },
        
        resume() {
            if (this.show && this.progress > 0) {
                this.isPaused = false;
                this.startCountdown();
            }
        }
    }
};

// Create global instance
window.GlobalAlert = new GlobalAlertSystem();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GlobalAlertSystem;
}
