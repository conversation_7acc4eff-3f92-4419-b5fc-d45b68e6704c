/**
 * Advanced Form Validation Utility
 */
export class FormValidator {
    constructor(form, options = {}) {
        this.form = typeof form === 'string' ? document.querySelector(form) : form;
        this.options = {
            validateOnInput: true,
            validateOnBlur: true,
            showErrors: true,
            errorClass: 'error',
            successClass: 'success',
            errorMessageClass: 'error-message',
            ...options
        };
        
        this.rules = {};
        this.errors = {};
        this.isValid = true;
        
        this.init();
    }
    
    init() {
        if (!this.form) return;
        
        // Prevent default form submission
        this.form.addEventListener('submit', (e) => {
            if (!this.validate()) {
                e.preventDefault();
                this.focusFirstError();
            }
        });
        
        // Add real-time validation
        if (this.options.validateOnInput || this.options.validateOnBlur) {
            this.addRealTimeValidation();
        }
    }
    
    /**
     * Add validation rules for a field
     */
    addRule(fieldName, rules) {
        this.rules[fieldName] = rules;
        return this;
    }
    
    /**
     * Add multiple rules at once
     */
    addRules(rulesObject) {
        Object.assign(this.rules, rulesObject);
        return this;
    }
    
    /**
     * Validate the entire form
     */
    validate() {
        this.errors = {};
        this.isValid = true;
        
        for (const [fieldName, rules] of Object.entries(this.rules)) {
            this.validateField(fieldName, rules);
        }
        
        if (this.options.showErrors) {
            this.displayErrors();
        }
        
        return this.isValid;
    }
    
    /**
     * Validate a single field
     */
    validateField(fieldName, rules) {
        const field = this.form.querySelector(`[name="${fieldName}"]`);
        if (!field) return true;
        
        const value = this.getFieldValue(field);
        const fieldErrors = [];
        
        for (const rule of rules) {
            const result = this.applyRule(value, rule, field);
            if (result !== true) {
                fieldErrors.push(result);
                this.isValid = false;
            }
        }
        
        if (fieldErrors.length > 0) {
            this.errors[fieldName] = fieldErrors;
        }
        
        return fieldErrors.length === 0;
    }
    
    /**
     * Apply a single validation rule
     */
    applyRule(value, rule, field) {
        const { type, message, ...params } = rule;
        
        switch (type) {
            case 'required':
                return this.validateRequired(value) || message || 'This field is required';
                
            case 'email':
                return this.validateEmail(value) || message || 'Please enter a valid email address';
                
            case 'phone':
                return this.validatePhone(value) || message || 'Please enter a valid phone number';
                
            case 'min':
                return this.validateMin(value, params.value) || message || `Minimum ${params.value} characters required`;
                
            case 'max':
                return this.validateMax(value, params.value) || message || `Maximum ${params.value} characters allowed`;
                
            case 'pattern':
                return this.validatePattern(value, params.pattern) || message || 'Invalid format';
                
            case 'match':
                const matchField = this.form.querySelector(`[name="${params.field}"]`);
                const matchValue = this.getFieldValue(matchField);
                return this.validateMatch(value, matchValue) || message || 'Fields do not match';
                
            case 'custom':
                return params.validator(value, field) || message || 'Invalid value';
                
            default:
                return true;
        }
    }
    
    /**
     * Validation methods
     */
    validateRequired(value) {
        return value !== null && value !== undefined && value.toString().trim() !== '';
    }
    
    validateEmail(value) {
        if (!value) return true; // Allow empty for non-required fields
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value);
    }
    
    validatePhone(value) {
        if (!value) return true;
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        return phoneRegex.test(value.replace(/\s/g, ''));
    }
    
    validateMin(value, min) {
        if (!value) return true;
        return value.toString().length >= min;
    }
    
    validateMax(value, max) {
        if (!value) return true;
        return value.toString().length <= max;
    }
    
    validatePattern(value, pattern) {
        if (!value) return true;
        const regex = new RegExp(pattern);
        return regex.test(value);
    }
    
    validateMatch(value1, value2) {
        return value1 === value2;
    }
    
    /**
     * Get field value based on field type
     */
    getFieldValue(field) {
        if (!field) return '';
        
        switch (field.type) {
            case 'checkbox':
                return field.checked;
            case 'radio':
                const radioGroup = this.form.querySelectorAll(`[name="${field.name}"]`);
                const checked = Array.from(radioGroup).find(radio => radio.checked);
                return checked ? checked.value : '';
            case 'select-multiple':
                return Array.from(field.selectedOptions).map(option => option.value);
            default:
                return field.value;
        }
    }
    
    /**
     * Display validation errors
     */
    displayErrors() {
        // Clear previous errors
        this.clearErrors();
        
        for (const [fieldName, fieldErrors] of Object.entries(this.errors)) {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (!field) continue;
            
            // Add error class to field
            field.classList.add(this.options.errorClass);
            field.classList.remove(this.options.successClass);
            
            // Create and display error message
            const errorElement = this.createErrorElement(fieldErrors[0]);
            this.insertErrorElement(field, errorElement);
        }
        
        // Add success class to valid fields
        for (const [fieldName] of Object.entries(this.rules)) {
            if (!this.errors[fieldName]) {
                const field = this.form.querySelector(`[name="${fieldName}"]`);
                if (field && this.getFieldValue(field)) {
                    field.classList.add(this.options.successClass);
                    field.classList.remove(this.options.errorClass);
                }
            }
        }
    }
    
    /**
     * Clear all validation errors
     */
    clearErrors() {
        const errorElements = this.form.querySelectorAll(`.${this.options.errorMessageClass}`);
        errorElements.forEach(element => element.remove());
        
        const fields = this.form.querySelectorAll(`.${this.options.errorClass}, .${this.options.successClass}`);
        fields.forEach(field => {
            field.classList.remove(this.options.errorClass, this.options.successClass);
        });
    }
    
    /**
     * Create error message element
     */
    createErrorElement(message) {
        const errorElement = document.createElement('div');
        errorElement.className = `${this.options.errorMessageClass} text-red-500 text-sm mt-1`;
        errorElement.textContent = message;
        return errorElement;
    }
    
    /**
     * Insert error element after field
     */
    insertErrorElement(field, errorElement) {
        const container = field.closest('.form-group') || field.parentNode;
        container.appendChild(errorElement);
    }
    
    /**
     * Focus first field with error
     */
    focusFirstError() {
        const firstErrorField = Object.keys(this.errors)[0];
        if (firstErrorField) {
            const field = this.form.querySelector(`[name="${firstErrorField}"]`);
            if (field) {
                field.focus();
                field.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    }
    
    /**
     * Add real-time validation
     */
    addRealTimeValidation() {
        for (const fieldName of Object.keys(this.rules)) {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (!field) continue;
            
            if (this.options.validateOnInput) {
                field.addEventListener('input', () => {
                    this.validateField(fieldName, this.rules[fieldName]);
                    this.displayErrors();
                });
            }
            
            if (this.options.validateOnBlur) {
                field.addEventListener('blur', () => {
                    this.validateField(fieldName, this.rules[fieldName]);
                    this.displayErrors();
                });
            }
        }
    }
    
    /**
     * Get validation errors
     */
    getErrors() {
        return this.errors;
    }
    
    /**
     * Check if form is valid
     */
    isFormValid() {
        return this.isValid;
    }
}
