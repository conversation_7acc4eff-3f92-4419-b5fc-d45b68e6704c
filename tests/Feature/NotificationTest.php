<?php

namespace Tests\Feature;

use App\Models\CustomerUser;
use App\Notifications\Auth\AuthEmailChangedNotification;
use App\Notifications\Auth\AuthPasswordChangedNotification;
use App\Notifications\Auth\AuthEmailVerifiedNotification;
use App\Notifications\ResetPasswordNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class NotificationTest extends TestCase
{
    use RefreshDatabase;

    public function test_auth_email_changed_notification_can_be_sent()
    {
        Notification::fake();

        $user = CustomerUser::factory()->create();
        $oldEmail = '<EMAIL>';

        Notification::route('mail', $oldEmail)
            ->notify(new AuthEmailChangedNotification($user, $oldEmail));

        Notification::assertSentTo(
            Notification::route('mail', $oldEmail),
            AuthEmailChangedNotification::class
        );
    }

    public function test_auth_password_changed_notification_can_be_sent()
    {
        Notification::fake();

        $user = CustomerUser::factory()->create();

        $user->notify(new AuthPasswordChangedNotification($user));

        Notification::assertSentTo(
            $user,
            AuthPasswordChangedNotification::class
        );
    }

    public function test_email_verified_notification_can_be_sent()
    {
        Notification::fake();

        $user = CustomerUser::factory()->create();

        $user->notify(new AuthEmailVerifiedNotification($user));

        Notification::assertSentTo(
            $user,
            AuthEmailVerifiedNotification::class
        );
    }

    public function test_reset_password_notification_can_be_sent()
    {
        Notification::fake();

        $user = CustomerUser::factory()->create();
        $token = 'test-token';

        $user->notify(new ResetPasswordNotification($token));

        Notification::assertSentTo(
            $user,
            ResetPasswordNotification::class
        );
    }
}
